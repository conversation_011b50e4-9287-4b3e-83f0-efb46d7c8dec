{"name": "@coozf/db", "version": "0.1.0", "private": true, "type": "module", "exports": {".": {"types": "./dist/index.d.ts", "default": "./src/index.ts"}, "./client": {"types": "./dist/client.d.ts", "default": "./src/client.ts"}, "./schema": {"types": "./dist/schema.d.ts", "default": "./src/schema.ts"}}, "license": "MIT", "scripts": {"build": "tsc", "clean": "git clean -xdf .cache .turbo dist node_modules", "dev": "tsc", "format": "prettier --check . --ignore-path ../../.gitignore", "lint": "eslint", "push": "pnpm with-env prisma db push", "studio": "pnpm with-env prisma studio", "generate": "pnpm with-env prisma generate", "migrate": "pnpm with-env prisma migrate dev", "reset": "pnpm with-env prisma migrate reset", "check-types": "tsc --noEmit --emitDeclarationOnly false", "with-env": "dotenv -e ../../.env.local --"}, "dependencies": {"@prisma/client": "^6.11.1", "mysql2": "^3.14.1", "zod": "catalog:"}, "devDependencies": {"@repo/eslint-config": "workspace:*", "@repo/typescript-config": "workspace:*", "dotenv-cli": "^8.0.0", "prisma": "^6.11.1"}, "prettier": "@acme/prettier-config"}