{"name": "@coozf/db", "version": "0.1.0", "private": true, "type": "module", "exports": {".": {"types": "./dist/index.d.ts", "default": "./src/index.ts"}, "./client": {"types": "./dist/client.d.ts", "default": "./src/client.ts"}, "./schema": {"types": "./dist/schema.d.ts", "default": "./src/schema.ts"}}, "license": "MIT", "scripts": {"build": "tsc", "clean": "git clean -xdf .cache .turbo dist node_modules", "dev": "tsc", "format": "prettier --check . --ignore-path ../../.gitignore", "lint": "eslint", "push": "pnpm with-env drizzle-kit push", "studio": "pnpm with-env drizzle-kit studio", "check-types": "tsc --noEmit --emitDeclarationOnly false", "with-env": "dotenv -e ../../.env.local --"}, "dependencies": {"@vercel/postgres": "^0.10.0", "drizzle-orm": "^0.44.2", "drizzle-zod": "^0.7.1", "pg": "^8.16.2", "zod": "catalog:"}, "devDependencies": {"@repo/eslint-config": "workspace:*", "@repo/typescript-config": "workspace:*", "dotenv-cli": "^8.0.0", "@types/pg": "^8.15.4", "drizzle-kit": "^0.31.1"}, "prettier": "@acme/prettier-config"}