import { z } from 'zod'

const envSchema = z.object({
    DATABASE_URL: z.string(),
    DEFAULT_TIMEZONE: z.string().default('Asia/Shanghai'),
    REDIS_HOST: z.string().default('localhost'),
    REDIS_PORT: z.string().transform(Number).default('6379'),
    REDIS_PASSWORD: z.string().default(''),
    REDIS_USERNAME: z.string().default('default'),
    REDIS_DB: z.string().transform(Number).default('0'),
})

export const env = envSchema.parse(process.env)