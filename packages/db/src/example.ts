/**
 * 使用示例：展示如何使用新的 Prisma + MySQL 数据库层
 * 
 * 这个文件展示了如何使用 Repository 模式进行数据库操作
 */

import { db } from './client'
import {
  UserRepository,
  ApplicationRepository,
  OrderRepository,
  ApplicationBalanceRepository,
  TransactionRepository,
  ApiCallRepository,
  AuthAccountRepository,
} from './repositories'

// 创建 Repository 实例
const userRepo = new UserRepository(db)
const appRepo = new ApplicationRepository(db)
const orderRepo = new OrderRepository(db)
const balanceRepo = new ApplicationBalanceRepository(db)
const transactionRepo = new TransactionRepository(db)
const apiCallRepo = new ApiCallRepository(db)
const authAccountRepo = new AuthAccountRepository(db)

/**
 * 示例：创建用户和应用
 */
export async function createUserAndApplication() {
  try {
    // 1. 创建用户
    const user = await userRepo.create({
      email: '<EMAIL>',
      name: '测试用户',
      password: 'password123',
    })
    console.log('创建用户成功:', user)

    // 2. 创建应用
    const application = await appRepo.create({
      name: '测试应用',
      description: '这是一个测试应用',
      appId: `app_${Date.now()}`, // 生成唯一的应用ID
      secret: 'test_secret_key', // 在实际应用中应该生成加密的密钥
      webhookSecret: 'webhook_secret_key', // webhook 密钥
      user: {
        connect: { id: user.id }
      }
    })
    console.log('创建应用成功:', application)

    // 3. 创建应用余额记录
    const balance = await balanceRepo.create({
      balance: 100.00,
      application: {
        connect: { id: application.id }
      }
    })
    console.log('创建余额记录成功:', balance)

    return { user, application, balance }
  } catch (error) {
    console.error('创建失败:', error)
    throw error
  }
}

/**
 * 示例：创建订单和交易记录
 */
export async function createOrderAndTransaction(userId: string, applicationId: string) {
  try {
    // 1. 创建订单
    const order = await orderRepo.createWithOrderNo({
      antCoins: 50.00,
      amount: 50.00,
      type: 'PURCHASE',
      user: {
        connect: { id: userId }
      },
      application: {
        connect: { id: applicationId }
      }
    })
    console.log('创建订单成功:', order)

    // 2. 创建交易记录（充值）
    const transaction = await transactionRepo.createWithBalanceUpdate({
      applicationId,
      type: 'RECHARGE',
      amount: 50.00,
      description: `订单充值: ${order.orderNo}`,
      relatedId: order.id,
      relatedType: 'order',
    })
    console.log('创建交易记录成功:', transaction)

    return { order, transaction }
  } catch (error) {
    console.error('创建订单失败:', error)
    throw error
  }
}

/**
 * 示例：记录 API 调用
 */
export async function recordApiCall(applicationId: string) {
  try {
    const apiCall = await apiCallRepo.recordApiCallWithCost({
      applicationId,
      endpoint: '/api/xiaohongshu/posts',
      method: 'GET',
      costType: 'ACCOUNT_QUOTA',
      costAmount: 0.1,
      statusCode: 200,
    })
    console.log('记录 API 调用成功:', apiCall)

    return apiCall
  } catch (error) {
    console.error('记录 API 调用失败:', error)
    throw error
  }
}

/**
 * 示例：添加授权账号
 */
export async function addAuthAccount(appId: string) {
  try {
    const authAccount = await authAccountRepo.create({
      platform: 'xiaohongshu',
      platformUserId: 'xhs_user_123',
      userInfo: {
        nickname: '测试用户',
        avatar: 'https://example.com/avatar.jpg',
      },
      scope: 'read,write',
      application: {
        connect: { id: appId }
      }
    })
    console.log('添加授权账号成功:', authAccount)

    return authAccount
  } catch (error) {
    console.error('添加授权账号失败:', error)
    throw error
  }
}

/**
 * 示例：查询数据
 */
export async function queryData(userId: string) {
  try {
    // 查询用户及其应用
    const userWithApps = await userRepo.findWithApplications(userId)
    console.log('用户及应用:', userWithApps)

    // 查询用户的订单
    const orders = await orderRepo.findByUserId(userId, { take: 10 })
    console.log('用户订单:', orders)

    // 查询订单统计
    const orderStats = await orderRepo.getOrderStats({ userId })
    console.log('订单统计:', orderStats)

    return { userWithApps, orders, orderStats }
  } catch (error) {
    console.error('查询数据失败:', error)
    throw error
  }
}

/**
 * 完整示例流程
 */
export async function fullExample() {
  try {
    console.log('开始完整示例流程...')

    // 1. 创建用户和应用
    const { user, application } = await createUserAndApplication()

    // 2. 创建订单和交易
    await createOrderAndTransaction(user.id, application.id)

    // 3. 记录 API 调用
    await recordApiCall(application.id)

    // 4. 添加授权账号
    await addAuthAccount(application.id)

    // 5. 查询数据
    await queryData(user.id)

    console.log('完整示例流程执行成功！')
  } catch (error) {
    console.error('示例流程执行失败:', error)
  } finally {
    // 关闭数据库连接
    await db.$disconnect()
  }
}

// 如果直接运行此文件，执行示例
if (require.main === module) {
  fullExample()
}
