import { z } from 'zod'

// 订单枚举
export const OrderSourceEnum = z.enum(['SYSTEM'])
export const OrderTypeEnum = z.enum(['PURCHASE', 'GIFT'])
export const PaymentMethodEnum = z.enum(['BANK_TRANSFER'])
export const OrderStatusEnum = z.enum(['PENDING', 'COMPLETED', 'CANCELLED'])

// 订单相关的 Zod Schema
export const CreateOrderSchema = z.object({
  antCoins: z.number().min(0.01, '蚁贝数量必须大于0'),
  amount: z.number().min(0.01, '金额必须大于0'),
  type: OrderTypeEnum,
  source: OrderSourceEnum.default('SYSTEM'),
  paymentMethod: PaymentMethodEnum.default('BANK_TRANSFER'),
  status: OrderStatusEnum.default('COMPLETED'),
  remarks: z.string().max(500, '备注不能超过500个字符').optional(),
})

export const UpdateOrderSchema = z.object({
  status: OrderStatusEnum.optional(),
  invoiceRequested: z.boolean().optional(),
  remarks: z.string().max(500, '备注不能超过500个字符').optional(),
})

export const SelectOrderSchema = z.object({
  id: z.string(),
  orderNo: z.string(),
  userId: z.string(),
  applicationId: z.string(),
  antCoins: z.number(),
  amount: z.number(),
  source: OrderSourceEnum,
  type: OrderTypeEnum,
  paymentMethod: PaymentMethodEnum,
  status: OrderStatusEnum,
  invoiceRequested: z.boolean(),
  remarks: z.string().nullable(),
  createdAt: z.date(),
  updatedAt: z.date(),
})

// 订单列表查询参数
export const OrderListSchema = z.object({
  page: z.number().min(1).default(1),
  pageSize: z.number().min(1).max(100).default(10),
  userId: z.string().optional(),
  applicationId: z.string().optional(),
  status: OrderStatusEnum.optional(),
  type: OrderTypeEnum.optional(),
  startDate: z.string().optional(),
  endDate: z.string().optional(),
  search: z.string().optional(), // 搜索订单号
})

// 管理员充值Schema
export const AdminRechargeSchema = z.object({
  applicationId: z.string().min(1, '请选择应用'),
  amount: z.number().min(0.01, '充值金额必须大于0'),
  type: OrderTypeEnum.default('GIFT'),
  remarks: z.string().max(500, '备注不能超过500个字符').optional(),
})

// 类型推导
export type OrderSource = z.infer<typeof OrderSourceEnum>
export type OrderType = z.infer<typeof OrderTypeEnum>
export type PaymentMethod = z.infer<typeof PaymentMethodEnum>
export type OrderStatus = z.infer<typeof OrderStatusEnum>
export type CreateOrderInput = z.infer<typeof CreateOrderSchema>
export type UpdateOrderInput = z.infer<typeof UpdateOrderSchema>
export type SelectOrderOutput = z.infer<typeof SelectOrderSchema>
export type OrderListParams = z.infer<typeof OrderListSchema>
export type AdminRechargeParams = z.infer<typeof AdminRechargeSchema>
