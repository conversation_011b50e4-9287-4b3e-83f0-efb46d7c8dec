import { z } from 'zod'

// 应用状态枚举
export const ApplicationStatusEnum = z.enum(['ACTIVE', 'SUSPENDED', 'DELETED'])

// 应用相关的 Zod Schema
export const CreateApplicationSchema = z.object({
  name: z.string().min(1, '应用名称不能为空').max(100, '应用名称不能超过100个字符'),
  description: z.string().max(500, '应用描述不能超过500个字符').optional(),
  webhookUrl: z.string().url('Webhook URL格式错误').optional(),
})

export const UpdateApplicationSchema = z.object({
  name: z.string().min(1, '应用名称不能为空').max(100, '应用名称不能超过100个字符').optional(),
  description: z.string().max(500, '应用描述不能超过500个字符').optional(),
  webhookUrl: z.string().url('Webhook URL格式错误').optional(),
  status: z.enum(['ACTIVE', 'SUSPENDED']).optional(), // 不允许直接设置为 DELETED
})

export const SelectApplicationSchema = z.object({
  id: z.string(),
  userId: z.string(),
  appId: z.string(),
  name: z.string(),
  description: z.string().nullable(),
  secret: z.string(), // 注意：在实际使用中应该过滤掉
  status: ApplicationStatusEnum,
  webhookUrl: z.string().nullable(),
  webhookSecret: z.string(), // 注意：在实际使用中应该过滤掉
  createdAt: z.date(),
  updatedAt: z.date(),
})

// 应用列表查询参数
export const ApplicationListSchema = z.object({
  page: z.number().min(1).default(1),
  pageSize: z.number().min(1).max(100).default(10),
  userId: z.string().optional(),
  status: ApplicationStatusEnum.optional(),
  search: z.string().optional(), // 搜索应用名称
})

// 类型推导
export type ApplicationStatus = z.infer<typeof ApplicationStatusEnum>
export type CreateApplicationInput = z.infer<typeof CreateApplicationSchema>
export type UpdateApplicationInput = z.infer<typeof UpdateApplicationSchema>
export type SelectApplicationOutput = z.infer<typeof SelectApplicationSchema>
export type ApplicationListParams = z.infer<typeof ApplicationListSchema>
