import { z } from 'zod'

// 应用状态枚举
export const ApplicationStatusEnum = z.enum(['ACTIVE', 'INACTIVE', 'SUSPENDED'])

// 应用相关的 Zod Schema
export const CreateApplicationSchema = z.object({
  name: z.string().min(1, '应用名称不能为空').max(100, '应用名称不能超过100个字符'),
  description: z.string().max(1000, '应用描述不能超过1000个字符').optional(),
  status: ApplicationStatusEnum.default('ACTIVE'),
})

export const UpdateApplicationSchema = z.object({
  name: z.string().min(1, '应用名称不能为空').max(100, '应用名称不能超过100个字符').optional(),
  description: z.string().max(1000, '应用描述不能超过1000个字符').optional(),
  status: ApplicationStatusEnum.optional(),
})

export const SelectApplicationSchema = z.object({
  id: z.string(),
  userId: z.string(),
  name: z.string(),
  description: z.string().nullable(),
  status: ApplicationStatusEnum,
  createdAt: z.date(),
  updatedAt: z.date(),
})

// 应用列表查询参数
export const ApplicationListSchema = z.object({
  page: z.number().min(1).default(1),
  pageSize: z.number().min(1).max(100).default(10),
  userId: z.string().optional(),
  status: ApplicationStatusEnum.optional(),
  search: z.string().optional(), // 搜索应用名称
})

// 类型推导
export type ApplicationStatus = z.infer<typeof ApplicationStatusEnum>
export type CreateApplicationInput = z.infer<typeof CreateApplicationSchema>
export type UpdateApplicationInput = z.infer<typeof UpdateApplicationSchema>
export type SelectApplicationOutput = z.infer<typeof SelectApplicationSchema>
export type ApplicationListParams = z.infer<typeof ApplicationListSchema>
