import { z } from 'zod'

// 交易类型枚举
export const TransactionTypeEnum = z.enum(['RECHARGE', 'CONSUME', 'REFUND'])

// API调用成本类型枚举
export const ApiCostTypeEnum = z.enum(['ACCOUNT_QUOTA', 'TRAFFIC'])

// 应用余额相关的 Zod Schema
export const CreateApplicationBalanceSchema = z.object({
  balance: z.number().min(0, '余额不能为负数').default(0),
})

export const UpdateApplicationBalanceSchema = z.object({
  balance: z.number().min(0, '余额不能为负数').optional(),
})

// 交易记录相关的 Zod Schema
export const CreateTransactionSchema = z.object({
  type: TransactionTypeEnum,
  amount: z.number().min(0.01, '金额必须大于0'),
  description: z.string().max(500, '描述不能超过500个字符').optional(),
  relatedId: z.string().min(1, '关联ID不能为空'),
  relatedType: z.string().min(1, '关联类型不能为空'),
})

export const SelectTransactionSchema = z.object({
  id: z.string(),
  applicationId: z.string(),
  type: TransactionTypeEnum,
  amount: z.number(),
  beforeBalance: z.number(),
  afterBalance: z.number(),
  description: z.string().nullable(),
  relatedId: z.string(),
  relatedType: z.string(),
  createdAt: z.date(),
  updatedAt: z.date(),
})

// API调用记录相关的 Zod Schema
export const CreateApiCallSchema = z.object({
  endpoint: z.string().min(1, 'API端点不能为空'),
  method: z.enum(['GET', 'POST', 'PUT', 'DELETE', 'PATCH']),
  costType: ApiCostTypeEnum,
  costAmount: z.number().min(0, '扣费金额不能为负数'),
  statusCode: z.number().optional(),
})

export const SelectApiCallSchema = z.object({
  id: z.string(),
  applicationId: z.string(),
  endpoint: z.string(),
  method: z.string(),
  costType: ApiCostTypeEnum,
  costAmount: z.number(),
  statusCode: z.number().nullable(),
  createdAt: z.date(),
  updatedAt: z.date(),
})

// 交易记录查询参数
export const TransactionListSchema = z.object({
  page: z.number().min(1).default(1),
  pageSize: z.number().min(1).max(100).default(10),
  applicationId: z.string().optional(),
  type: TransactionTypeEnum.optional(),
  startDate: z.string().optional(),
  endDate: z.string().optional(),
})

// API调用记录查询参数
export const ApiCallListSchema = z.object({
  page: z.number().min(1).default(1),
  pageSize: z.number().min(1).max(100).default(10),
  applicationId: z.string().optional(),
  endpoint: z.string().optional(),
  costType: ApiCostTypeEnum.optional(),
  startDate: z.string().optional(),
  endDate: z.string().optional(),
})

// 类型推导
export type TransactionType = z.infer<typeof TransactionTypeEnum>
export type ApiCostType = z.infer<typeof ApiCostTypeEnum>
export type CreateApplicationBalanceInput = z.infer<typeof CreateApplicationBalanceSchema>
export type UpdateApplicationBalanceInput = z.infer<typeof UpdateApplicationBalanceSchema>
export type CreateTransactionInput = z.infer<typeof CreateTransactionSchema>
export type SelectTransactionOutput = z.infer<typeof SelectTransactionSchema>
export type CreateApiCallInput = z.infer<typeof CreateApiCallSchema>
export type SelectApiCallOutput = z.infer<typeof SelectApiCallSchema>
export type TransactionListParams = z.infer<typeof TransactionListSchema>
export type ApiCallListParams = z.infer<typeof ApiCallListSchema>
