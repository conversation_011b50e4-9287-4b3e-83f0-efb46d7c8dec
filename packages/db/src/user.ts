import { pgTable } from 'drizzle-orm/pg-core'
import { createInsertSchema, createSelectSchema } from 'drizzle-zod'
import z from 'zod'
import { timestamps } from './columns.helpers'

// 用户表
export const users = pgTable('users', (t) => ({
  id: t.uuid().notNull().primaryKey().defaultRandom(),
  email: t.varchar({ length: 255 }).unique(),
  phone: t.varchar({ length: 20 }).unique(),
  password: t.varchar({ length: 255 }),
  name: t.varchar({ length: 100 }),
  phoneVerified: t.boolean().default(false).notNull(),
  emailVerified: t.boolean().default(false).notNull(),
  avatar: t.varchar({ length: 500 }),
  ...timestamps,
}))

// 类型推导
export type User = typeof users.$inferSelect
export type InsertUser = typeof users.$inferInsert

// 用户相关的 Zod Schema
export const CreateUserSchema = createInsertSchema(users, {
  email: z.string().email('请输入有效的邮箱地址').optional(),
  phone: z
    .string()
    .regex(/^1[3-9]\d{9}$/, '请输入有效的手机号')
    .optional(),
  password: z.string().min(6, '密码至少6位').optional(),
  name: z.string().min(1, '姓名不能为空').optional(),
}).omit({
  id: true,
  createdAt: true,
  updatedAt: true,
  phoneVerified: true,
  emailVerified: true,
})

export const LoginSchema = z
  .object({
    email: z.string().email('请输入有效的邮箱地址').optional(),
    phone: z
      .string()
      .regex(/^1[3-9]\d{9}$/, '请输入有效的手机号')
      .optional(),
    password: z.string().min(6, '密码至少6位'),
  })
  .refine((data) => data.email ?? data.phone, {
    message: '请输入邮箱或手机号',
  })

export const SelectUserSchema = createSelectSchema(users).omit({
  password: true, // 不返回密码
})
