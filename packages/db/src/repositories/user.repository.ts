import { User, Prisma, PrismaClient } from '../generated/prisma'
import { BaseRepository } from './base.repository'

export type CreateUserInput = Prisma.UserCreateInput
export type UpdateUserInput = Prisma.UserUpdateInput

/**
 * 用户 Repository
 */
export class UserRepository extends BaseRepository<User, CreateUserInput, UpdateUserInput> {
  constructor(db: PrismaClient) {
    super(db)
  }

  /**
   * 创建用户
   */
  async create(data: CreateUserInput): Promise<User> {
    return this.db.user.create({
      data,
    })
  }

  /**
   * 根据 ID 查找用户
   */
  async findById(id: string): Promise<User | null> {
    return this.db.user.findUnique({
      where: { id },
    })
  }

  /**
   * 根据邮箱查找用户
   */
  async findByEmail(email: string): Promise<User | null> {
    return this.db.user.findUnique({
      where: { email },
    })
  }

  /**
   * 根据手机号查找用户
   */
  async findByPhone(phone: string): Promise<User | null> {
    return this.db.user.findUnique({
      where: { phone },
    })
  }

  /**
   * 查找多个用户
   */
  async findMany(params?: {
    skip?: number
    take?: number
    where?: Prisma.UserWhereInput
    orderBy?: Prisma.UserOrderByWithRelationInput
    include?: Prisma.UserInclude
  }): Promise<User[]> {
    return this.db.user.findMany(params)
  }

  /**
   * 更新用户
   */
  async update(id: string, data: UpdateUserInput): Promise<User> {
    return this.db.user.update({
      where: { id },
      data,
    })
  }

  /**
   * 删除用户
   */
  async delete(id: string): Promise<User> {
    return this.db.user.delete({
      where: { id },
    })
  }

  /**
   * 统计用户数量
   */
  async count(where?: Prisma.UserWhereInput): Promise<number> {
    return this.db.user.count({ where })
  }

  /**
   * 验证用户邮箱
   */
  async verifyEmail(id: string): Promise<User> {
    return this.db.user.update({
      where: { id },
      data: { emailVerified: true },
    })
  }

  /**
   * 验证用户手机号
   */
  async verifyPhone(id: string): Promise<User> {
    return this.db.user.update({
      where: { id },
      data: { phoneVerified: true },
    })
  }

  /**
   * 更新用户密码
   */
  async updatePassword(id: string, password: string): Promise<User> {
    return this.db.user.update({
      where: { id },
      data: { password },
    })
  }

  /**
   * 获取用户及其应用
   */
  async findWithApplications(id: string): Promise<User | null> {
    return this.db.user.findUnique({
      where: { id },
      include: {
        applications: true,
      },
    })
  }
}
