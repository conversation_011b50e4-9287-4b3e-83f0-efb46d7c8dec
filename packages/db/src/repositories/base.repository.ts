import { PrismaClient } from '../generated/prisma'

/**
 * 基础 Repository 类
 * 提供通用的 CRUD 操作接口
 */
export abstract class BaseRepository<T, CreateInput, UpdateInput> {
  protected db: PrismaClient

  constructor(db: PrismaClient) {
    this.db = db
  }

  /**
   * 创建记录
   */
  abstract create(data: CreateInput): Promise<T>

  /**
   * 根据 ID 查找记录
   */
  abstract findById(id: string): Promise<T | null>

  /**
   * 查找多条记录
   */
  abstract findMany(params?: {
    skip?: number
    take?: number
    where?: any
    orderBy?: any
    include?: any
  }): Promise<T[]>

  /**
   * 更新记录
   */
  abstract update(id: string, data: UpdateInput): Promise<T>

  /**
   * 删除记录
   */
  abstract delete(id: string): Promise<T>

  /**
   * 统计记录数量
   */
  abstract count(where?: any): Promise<number>

  /**
   * 检查记录是否存在
   */
  async exists(id: string): Promise<boolean> {
    const record = await this.findById(id)
    return record !== null
  }
}
