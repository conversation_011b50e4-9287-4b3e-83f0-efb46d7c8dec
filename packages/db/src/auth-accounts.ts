import { pgTable, index } from 'drizzle-orm/pg-core'
import { createInsertSchema, createSelectSchema } from 'drizzle-zod'
import { z } from 'zod'
import { timestamps } from './columns.helpers'
import { applications } from './application'

// 授权账号表
export const authAccounts = pgTable(
  'auth_accounts',
  (t) => ({
    id: t.uuid().notNull().primaryKey().defaultRandom(),
    appId: t
      .uuid()
      .notNull()
      .references(() => applications.id, { onDelete: 'cascade' }),
    platform: t.varchar({ length: 20 }).notNull(), // 平台标识：xiaohongshu, douyin, kuaishou 等
    platformUserId: t.varchar({ length: 100 }).notNull(), // 平台用户ID
    userInfo: t.jsonb().$type<{
      nickname?: string
      avatar?: string
      userId?: string
      [key: string]: unknown
    }>(), // 用户基本信息
    state: t.varchar({ length: 100 }), // OAuth状态参数
    scope: t.varchar({ length: 200 }), // 授权范围
    ...timestamps,
  }),
  (table) => [
    index('auth_accounts_app_id_idx').on(table.appId),
    index('auth_accounts_platform_idx').on(table.platform),
    index('auth_accounts_platform_user_id_idx').on(table.platformUserId),
  ]
)

// 类型推导
export type AuthAccount = typeof authAccounts.$inferSelect
export type InsertAuthAccount = typeof authAccounts.$inferInsert

// 授权账号相关的 Zod Schema
export const CreateAuthAccountSchema = createInsertSchema(authAccounts, {
  appId: z.string().uuid('应用ID格式错误'),
  platform: z.enum(['xiaohongshu', 'douyin', 'kuaishou', 'weibo'], {
    errorMap: () => ({ message: '不支持的平台' }),
  }),
  platformUserId: z.string().min(1, '平台用户ID不能为空'),
  state: z.string().optional(),
  scope: z.string().optional(),
}).omit({
  id: true,
  createdAt: true,
  updatedAt: true,
})

export const SelectAuthAccountSchema = createSelectSchema(authAccounts)

// OAuth授权请求Schema
export const OAuthAuthorizeSchema = z.object({
  platform: z.enum(['xiaohongshu', 'douyin', 'kuaishou', 'weibo'], {
    errorMap: () => ({ message: '不支持的平台' }),
  }),
})

// OAuth回调Schema
export const OAuthCallbackSchema = z.object({
  code: z.string().min(1, '授权码不能为空'),
  state: z.string().min(1, '状态参数不能为空'),
})

// API透传Schema
export const ApiProxySchema = z.object({
  accessToken: z.string().min(1, 'Access Token不能为空'),
  apiPath: z.string().min(1, 'API路径不能为空'),
  method: z.enum(['GET', 'POST', 'PUT', 'DELETE', 'PATCH']).default('POST'),
  params: z.record(z.unknown()).optional(),
  headers: z.record(z.string()).optional(),
})
