# Schema 迁移验证报告

## 概述

本文档验证了从 Drizzle ORM + PostgreSQL 到 Prisma ORM + MySQL 的 schema 迁移完整性。

## ✅ 已验证的表结构

### 1. 用户表 (users)
**原始 Drizzle 定义 → Prisma 定义**
- ✅ `id: uuid` → `String @id @default(cuid())`
- ✅ `email: varchar(255)` → `String? @unique @db.VarChar(255)`
- ✅ `phone: varchar(20)` → `String? @unique @db.VarChar(20)`
- ✅ `password: varchar(255)` → `String? @db.VarChar(255)`
- ✅ `name: varchar(100)` → `String? @db.VarChar(100)`
- ✅ `phoneVerified: boolean` → `<PERSON>olean @default(false)`
- ✅ `emailVerified: boolean` → `Boolean @default(false)`
- ✅ `avatar: varchar(500)` → `String? @db.VarChar(500)`
- ✅ `timestamps` → `createdAt: DateTime @default(now())`, `updatedAt: DateTime @updatedAt`

### 2. 应用表 (applications)
**原始 Drizzle 定义 → Prisma 定义**
- ✅ `id: uuid` → `String @id @default(cuid())`
- ✅ `userId: uuid` → `String` (外键)
- ✅ `appId: varchar(64)` → `String @unique @db.VarChar(64)`
- ✅ `name: varchar(100)` → `String @db.VarChar(100)`
- ✅ `description: text` → `String? @db.Text`
- ✅ `secret: varchar(255)` → `String @db.VarChar(255)`
- ✅ `status: varchar(20)` → `ApplicationStatus @default(ACTIVE)`
- ✅ `webhookUrl: varchar(500)` → `String? @db.VarChar(500)`
- ✅ `webhookSecret: varchar(255)` → `String @db.VarChar(255)`
- ✅ `timestamps` → `createdAt: DateTime @default(now())`, `updatedAt: DateTime @updatedAt`

**索引迁移**
- ✅ `applications_user_id_idx` → `@@index([userId])`
- ✅ `applications_app_id_idx` → `@@index([appId])`
- ✅ `applications_status_idx` → `@@index([status])`

**枚举迁移**
- ✅ `active, suspended, deleted` → `ACTIVE, SUSPENDED, DELETED`

### 3. 订单表 (orders)
**原始 Drizzle 定义 → Prisma 定义**
- ✅ `id: uuid` → `String @id @default(cuid())`
- ✅ `orderNo: varchar(32)` → `String @unique @db.VarChar(32)`
- ✅ `userId: uuid` → `String` (外键)
- ✅ `applicationId: uuid` → `String` (外键)
- ✅ `antCoins: numeric(10,2)` → `Decimal @db.Decimal(10,2)`
- ✅ `amount: numeric(10,2)` → `Decimal @db.Decimal(10,2)`
- ✅ `source: varchar(20)` → `OrderSource @default(SYSTEM)`
- ✅ `type: varchar(20)` → `OrderType`
- ✅ `paymentMethod: varchar(50)` → `PaymentMethod @default(BANK_TRANSFER)`
- ✅ `status: varchar(20)` → `OrderStatus @default(COMPLETED)`
- ✅ `invoiceRequested: boolean` → `Boolean @default(false)`
- ✅ `remarks: varchar(500)` → `String? @db.VarChar(500)`
- ✅ `timestamps` → `createdAt: DateTime @default(now())`, `updatedAt: DateTime @updatedAt`

**索引迁移**
- ✅ `orders_order_no_idx` → `@@index([orderNo])`
- ✅ `orders_user_id_idx` → `@@index([userId])`
- ✅ `orders_status_idx` → `@@index([status])`
- ✅ `orders_type_idx` → `@@index([type])`
- ✅ `orders_created_at_idx` → `@@index([createdAt])`

### 4. 应用余额表 (application_balances)
**原始 Drizzle 定义 → Prisma 定义**
- ✅ `id: uuid` → `String @id @default(cuid())`
- ✅ `applicationId: uuid` → `String @unique` (外键)
- ✅ `balance: numeric(10,2)` → `Decimal @default(0.00) @db.Decimal(10,2)`
- ✅ `timestamps` → `createdAt: DateTime @default(now())`, `updatedAt: DateTime @updatedAt`

### 5. 交易记录表 (transactions)
**原始 Drizzle 定义 → Prisma 定义**
- ✅ `id: uuid` → `String @id @default(cuid())`
- ✅ `applicationId: uuid` → `String` (外键)
- ✅ `type: varchar(20)` → `TransactionType`
- ✅ `amount: numeric(10,2)` → `Decimal @db.Decimal(10,2)`
- ✅ `beforeBalance: numeric(10,2)` → `Decimal @db.Decimal(10,2)`
- ✅ `afterBalance: numeric(10,2)` → `Decimal @db.Decimal(10,2)`
- ✅ `description: varchar(500)` → `String? @db.VarChar(500)`
- ✅ `relatedId: uuid` → `String`
- ✅ `relatedType: varchar(20)` → `String @db.VarChar(20)`
- ✅ `timestamps` → `createdAt: DateTime @default(now())`, `updatedAt: DateTime @updatedAt`

**索引迁移**
- ✅ `transactions_app_id_idx` → `@@index([applicationId])`
- ✅ `transactions_type_idx` → `@@index([type])`
- ✅ `transactions_created_at_idx` → `@@index([createdAt])`

### 6. API调用记录表 (api_calls)
**原始 Drizzle 定义 → Prisma 定义**
- ✅ `id: uuid` → `String @id @default(cuid())`
- ✅ `applicationId: uuid` → `String` (外键)
- ✅ `endpoint: varchar(255)` → `String @db.VarChar(255)`
- ✅ `method: varchar(10)` → `String @db.VarChar(10)`
- ✅ `costType: varchar(20)` → `ApiCostType`
- ✅ `costAmount: numeric(10,2)` → `Decimal @db.Decimal(10,2)`
- ✅ `statusCode: integer` → `Int?`
- ✅ `timestamps` → `createdAt: DateTime @default(now())`, `updatedAt: DateTime @updatedAt`

**索引迁移**
- ✅ `api_calls_app_id_idx` → `@@index([applicationId])`
- ✅ `api_calls_endpoint_idx` → `@@index([endpoint])`
- ✅ `api_calls_cost_type_idx` → `@@index([costType])`
- ✅ `api_calls_created_at_idx` → `@@index([createdAt])`

**枚举修正**
- ✅ `ACCOUNT_QUOTA, TRAFFIC` → `ACCOUNT_QUOTA, TRAFFIC` (保持一致)

### 7. 授权账号表 (auth_accounts)
**原始 Drizzle 定义 → Prisma 定义**
- ✅ `id: uuid` → `String @id @default(cuid())`
- ✅ `appId: uuid` → `String` (外键)
- ✅ `platform: varchar(20)` → `String @db.VarChar(20)`
- ✅ `platformUserId: varchar(100)` → `String @db.VarChar(100)`
- ✅ `userInfo: jsonb` → `Json?`
- ✅ `state: varchar(100)` → `String? @db.VarChar(100)`
- ✅ `scope: varchar(200)` → `String? @db.VarChar(200)`
- ✅ `timestamps` → `createdAt: DateTime @default(now())`, `updatedAt: DateTime @updatedAt`

**索引迁移**
- ✅ `auth_accounts_app_id_idx` → `@@index([appId])`
- ✅ `auth_accounts_platform_idx` → `@@index([platform])`
- ✅ `auth_accounts_platform_user_id_idx` → `@@index([platformUserId])`

## ✅ 关系定义验证

- ✅ User → Application (一对多)
- ✅ User → Order (一对多)
- ✅ Application → AuthAccount (一对多)
- ✅ Application → ApplicationBalance (一对一)
- ✅ Application → Transaction (一对多)
- ✅ Application → ApiCall (一对多)
- ✅ Application → Order (一对多)

## ✅ 枚举类型验证

- ✅ ApplicationStatus: ACTIVE, SUSPENDED, DELETED
- ✅ OrderSource: SYSTEM
- ✅ OrderType: PURCHASE, GIFT
- ✅ PaymentMethod: BANK_TRANSFER
- ✅ OrderStatus: PENDING, COMPLETED, CANCELLED
- ✅ TransactionType: RECHARGE, CONSUME, REFUND
- ✅ ApiCostType: ACCOUNT_QUOTA, TRAFFIC

## ✅ Zod Schema 验证

所有 Zod validation schemas 已更新以匹配新的 Prisma 类型，包括：
- ✅ 字段验证规则保持一致
- ✅ 枚举值正确映射
- ✅ 类型导出避免冲突

## 🔧 修正的问题

1. **应用表字段遗漏** - 已添加 `appId`, `secret`, `webhookUrl`, `webhookSecret`
2. **枚举值不匹配** - 修正 ApplicationStatus 枚举值
3. **ApiCostType 枚举** - 修正为 `ACCOUNT_QUOTA, TRAFFIC`
4. **索引定义** - 完整迁移所有索引
5. **类型冲突** - 解决 Prisma 生成类型与 Zod 枚举的冲突

## 📋 验证清单

- ✅ 所有表结构完整迁移
- ✅ 所有字段类型正确映射
- ✅ 所有索引正确定义
- ✅ 所有外键关系正确建立
- ✅ 所有枚举值正确定义
- ✅ 所有 Zod schemas 更新
- ✅ TypeScript 类型检查通过
- ✅ 项目构建成功
- ✅ Repository 层实现完整

## 🎯 结论

Schema 迁移已完成并通过验证。所有原始 Drizzle 定义都已正确转换为 Prisma 定义，保持了数据结构的完整性和业务逻辑的一致性。
