# 数据库迁移文档

## 概述

本次迁移将数据库技术栈从 **Drizzle ORM + PostgreSQL** 迁移到 **Prisma ORM + MySQL**。

## 迁移内容

### 1. 依赖变更

#### 移除的依赖
- `drizzle-orm`
- `drizzle-kit`
- `drizzle-zod`
- `@vercel/postgres`
- `pg`
- `@types/pg`

#### 新增的依赖
- `@prisma/client` - Prisma 客户端
- `prisma` - Prisma CLI 工具
- `mysql2` - MySQL 驱动

### 2. 配置文件变更

#### 删除的文件
- `drizzle.config.ts` - Drizzle 配置文件

#### 新增的文件
- `prisma/schema.prisma` - Prisma Schema 文件

### 3. 数据模型迁移

所有数据表都已从 Drizzle 语法转换为 Prisma 语法：

#### 用户表 (users)
- 主键：`uuid` → `String @id @default(cuid())`
- 时间戳：`timestamp with timezone` → `DateTime`
- 布尔值：保持 `Boolean`
- 字符串：`varchar(length)` → `String @db.VarChar(length)`

#### 应用表 (applications)
- 新增字段：`appId` (公开应用ID), `secret` (应用密钥), `webhookUrl`, `webhookSecret`
- 外键关系：使用 `@relation` 定义
- 枚举：`ApplicationStatus` 枚举 (ACTIVE, SUSPENDED, DELETED)
- 索引：使用 `@@index` 定义，包含 userId, appId, status

#### 订单表 (orders)
- 数值：`numeric(10,2)` → `Decimal @db.Decimal(10,2)`
- 枚举：定义了 `OrderSource`, `OrderType`, `PaymentMethod`, `OrderStatus`

#### 余额和交易表
- JSON 字段：`jsonb` → `Json`
- 精度数值：保持 `Decimal` 类型

#### 授权账号表 (auth_accounts)
- JSON 字段：`jsonb` → `Json`
- 复合索引：使用 `@@index` 定义

### 4. Repository 层实现

创建了完整的 Repository 模式：

#### 基础 Repository
- `BaseRepository<T, CreateInput, UpdateInput>` - 抽象基类
- 定义了标准的 CRUD 操作接口

#### 具体 Repository 类
- `UserRepository` - 用户数据操作
- `ApplicationRepository` - 应用数据操作
- `OrderRepository` - 订单数据操作
- `ApplicationBalanceRepository` - 应用余额操作
- `TransactionRepository` - 交易记录操作
- `ApiCallRepository` - API 调用记录操作
- `AuthAccountRepository` - 授权账号操作

### 5. 类型和验证

#### Zod Schema 重构
- 所有验证 Schema 都已重写以匹配 Prisma 类型
- 避免了与 Prisma 生成的枚举类型的冲突
- 保持了原有的验证逻辑

#### 类型导出
- 统一从 `./schema` 导出所有类型和验证 Schema
- 保持了向后兼容的 API 接口

### 6. 脚本更新

#### package.json 脚本变更
```json
{
  "push": "pnpm with-env prisma db push",
  "studio": "pnpm with-env prisma studio",
  "generate": "pnpm with-env prisma generate",
  "migrate": "pnpm with-env prisma migrate dev",
  "reset": "pnpm with-env prisma migrate reset"
}
```

## 使用方法

### 1. 环境配置

更新 `.env.local` 文件中的数据库连接字符串：

```env
# 从 PostgreSQL
DATABASE_URL=postgresql://user:password@host:port/database

# 改为 MySQL
DATABASE_URL=mysql://user:password@host:port/database
```

### 2. 数据库初始化

```bash
# 生成 Prisma 客户端
pnpm run generate

# 推送 Schema 到数据库
pnpm run push

# 或者使用迁移（推荐用于生产环境）
pnpm run migrate
```

### 3. 代码使用

```typescript
import { db } from '@coozf/db/client'
import { UserRepository } from '@coozf/db'

// 创建 Repository 实例
const userRepo = new UserRepository(db)

// 使用 Repository 进行数据操作
const user = await userRepo.create({
  email: '<EMAIL>',
  name: '用户名',
})
```

### 4. 查看数据

```bash
# 启动 Prisma Studio
pnpm run studio
```

## 主要优势

### 1. 类型安全
- Prisma 提供了更强的类型安全保证
- 自动生成的类型与数据库 Schema 完全同步

### 2. 开发体验
- Prisma Studio 提供了更好的数据库管理界面
- 更好的 IDE 支持和自动补全

### 3. 性能优化
- MySQL 在某些场景下性能更优
- Prisma 的查询优化器

### 4. 生态系统
- 更丰富的 Prisma 生态系统
- 更好的社区支持

## 注意事项

### 1. 数据迁移
- 需要将现有的 PostgreSQL 数据迁移到 MySQL
- 注意数据类型的兼容性

### 2. 查询语法
- 某些复杂查询可能需要调整
- JSON 字段的查询语法有所不同

### 3. 索引优化
- 重新评估和优化 MySQL 索引
- 考虑 MySQL 特有的索引类型

## 测试建议

1. **单元测试**：为所有 Repository 方法编写单元测试
2. **集成测试**：测试完整的业务流程
3. **性能测试**：对比迁移前后的性能表现
4. **数据一致性测试**：确保数据迁移的完整性

## 后续工作

1. 更新相关的业务逻辑代码以使用新的 Repository 层
2. 编写和运行测试用例
3. 性能调优和索引优化
4. 文档更新和团队培训
