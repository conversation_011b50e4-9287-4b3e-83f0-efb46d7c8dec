{"name": "@coozf/ui", "version": "0.0.0", "private": true, "license": "MIT", "sideEffects": false, "files": ["dist/**", "dist"], "exports": {"./counter-button": {"import": {"types": "./dist/es/counter-button.d.mts", "default": "./dist/es/counter-button.mjs"}, "require": {"types": "./dist/cjs/counter-button.d.ts", "default": "./dist/cjs/counter-button.js"}}, "./link": {"import": {"types": "./dist/es/link.d.mts", "default": "./dist/es/link.mjs"}, "require": {"types": "./dist/cjs/link.d.ts", "default": "./dist/cjs/link.js"}}}, "scripts": {"build": "bunchee", "dev": "bunchee --watch", "check-types": "tsc --noEmit", "lint": "eslint src/", "test": "jest"}, "devDependencies": {"@jest/globals": "^29.7.0", "@coozf/eslint-config": "workspace:*", "@coozf/typescript-config": "workspace:*", "@types/node": "^22.15.3", "bunchee": "^6.4.0", "eslint": "^9.30.0", "jest": "^29.7.0", "typescript": "5.8.2"}, "peerDependencies": {"@types/react": ">=18", "@types/react-dom": ">=18", "react": ">=18", "react-dom": ">=18"}}