{"$schema": "https://json.schemastore.org/tsconfig", "compilerOptions": {"composite": false, "declaration": true, "declarationMap": true, "esModuleInterop": true, "forceConsistentCasingInFileNames": true, "allowImportingTsExtensions": true, "inlineSources": false, "isolatedModules": true, "module": "ESNext", "moduleResolution": "<PERSON><PERSON><PERSON>", "noUnusedLocals": false, "noUnusedParameters": false, "preserveWatchOutput": true, "skipLibCheck": true, "strict": true, "noEmit": true, "strictNullChecks": true}, "exclude": ["node_modules"]}