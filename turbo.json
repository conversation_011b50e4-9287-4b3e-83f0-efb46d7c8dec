{"$schema": "https://turborepo.com/schema.json", "ui": "tui", "tasks": {"build": {"inputs": ["$TURBO_DEFAULT$", ".env*"], "dependsOn": ["^build"], "outputs": ["build/**", ".vercel/**", "dist/**", ".next/**", "!.next/cache/**"]}, "test": {"outputs": ["coverage/**"], "dependsOn": []}, "lint": {"dependsOn": ["^build", "^lint"]}, "check-types": {"dependsOn": ["^build", "^check-types"]}, "dev": {"dependsOn": ["^build"], "cache": false, "persistent": true}}}