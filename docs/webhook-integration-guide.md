# Webhook 集成与安全验证指南

为了确保您的应用能够安全、可靠地接收来自我们平台推送的事件通知，我们采用了基于 HMAC 的签名机制来验证每一条 Webhook 请求。请您在开发应用后端时，务必遵循本指南完成安全验证，否则将无法保证数据的真实性和完整性。

## 1. 获取您的 Webhook 签名密钥

在您创建或管理应用时，平台会为您生成一个唯一的 **Webhook 签名密钥 (Webhook Secret)**，格式为 `whsec_...`。

**请注意：**
- 这个密钥非常重要，请像对待 API Key 或数据库密码一样妥善保管。
- **强烈建议**将其存储在您后端服务的**环境变量**中，切勿硬编码在代码里。
- 此密钥仅用于验证 Webhook 签名，不用于其他 API 调用。

## 2. 验证 Webhook 签名的步骤

当您的服务器收到一条来自我们平台的 Webhook 请求时，HTTP 请求头中会包含以下两个关键字段：

- `X-Webhook-Timestamp`: 发送请求时的 Unix 时间戳 (秒)。
- `X-Webhook-Signature`: 本次请求的 HMAC-SHA256 签名。

您需要按照以下步骤在您的代码中验证签名：

### 步骤 1：提取请求信息

从收到的请求中，提取出三项信息：
1.  `X-Webhook-Timestamp` 请求头的值 (字符串)。
2.  `X-Webhook-Signature` 请求头的值 (字符串)。
3.  **原始的、未经解析的请求体 (Raw Request Body)** (字符串)。

> **警告：** 必须使用原始请求体。许多 Web 框架会自动将 JSON 请求体解析为对象。您需要找到一种方法来获取原始的、未经修改的请求体字符串，因为任何格式上的变动（如空格或换行）都会导致签名验证失败。

### 步骤 2：检查时间戳

为了防止“重放攻击”，您应该检查时间戳的有效性。

1.  将 `X-Webhook-Timestamp` 字符串转换为数字。
2.  获取您服务器当前的 Unix 时间戳 (秒)。
3.  计算两者之差。如果差值的绝对值过大（例如，超过 5 分钟），您应该拒绝该请求。

```javascript
const receivedTimestamp = parseInt(request.headers['x-webhook-timestamp'], 10);
const currentTimestamp = Math.floor(Date.now() / 1000);

if (Math.abs(currentTimestamp - receivedTimestamp) > 300) { // 5 minutes
  // 时间戳无效，拒绝请求
  return res.status(400).send('Timestamp validation failed');
}
```

### 步骤 3：拼接签名字符串

按照 `时间戳 + '.' + 原始请求体` 的格式，将您在步骤 1 中获取到的信息拼接成一个字符串。

```javascript
const stringToSign = `${request.headers['x-webhook-timestamp']}.${rawBody}`;
```

### 步骤 4：计算预期签名

使用 **HMAC-SHA256** 算法，以您的 **Webhook 签名密钥**作为 key，对上一步生成的 `stringToSign` 进行加密，并输出为十六进制字符串。

```javascript
const crypto = require('crypto');

const webhookSecret = process.env.YOUR_WEBHOOK_SECRET; // 从环境变量中获取密钥

const expectedSignature = crypto
  .createHmac('sha256', webhookSecret)
  .update(stringToSign)
  .digest('hex');
```

### 步骤 5：比较签名

将您在步骤 4 中计算出的 `expectedSignature` 与请求头中获取的 `X-Webhook-Signature` 进行比较。

> **安全提示：** 为了防止“时序攻击”，请使用专门的、时序安全的比较函数，而不是简单的 `===`。

```javascript
const receivedSignature = request.headers['x-webhook-signature'];

const signaturesMatch = crypto.timingSafeEqual(
  Buffer.from(receivedSignature, 'hex'),
  Buffer.from(expectedSignature, 'hex')
);

if (signaturesMatch) {
  // 签名验证成功！处理 Webhook 业务逻辑。
  console.log('Webhook validated successfully!');
  res.status(200).send('OK');
} else {
  // 签名无效，拒绝请求
  console.error('Webhook signature verification failed.');
  res.status(403).send('Invalid signature');
}
```

## 3. Node.js (Express) 完整示例

以下是一个使用 Express 框架的完整示例，展示了如何获取原始请求体并完成验证。

```javascript
const express = require('express');
const crypto = require('crypto');

// 注意：在 Express 4.17.0+ 中，你可以这样获取原始请求体
// 在定义 json parser 中间件时，加入 verify 函数
const app = express({
  json: {
    verify: (req, res, buf, encoding) => {
      if (buf && buf.length) {
        req.rawBody = buf.toString(encoding || 'utf8');
      }
    },
  },
});

app.post('/your-webhook-endpoint', (req, res) => {
  // 1. 提取请求信息
  const receivedSignature = req.headers['x-webhook-signature'];
  const receivedTimestamp = req.headers['x-webhook-timestamp'];
  const rawBody = req.rawBody;

  if (!receivedSignature || !receivedTimestamp || !rawBody) {
    return res.status(400).send('Missing webhook headers or body');
  }

  // 2. 检查时间戳
  const currentTimestamp = Math.floor(Date.now() / 1000);
  if (Math.abs(currentTimestamp - parseInt(receivedTimestamp, 10)) > 300) {
    return res.status(400).send('Timestamp validation failed');
  }

  // 3. 拼接签名字符串
  const stringToSign = `${receivedTimestamp}.${rawBody}`;

  // 4. 计算预期签名
  const webhookSecret = process.env.YOUR_WEBHOOK_SECRET;
  const expectedSignature = crypto
    .createHmac('sha256', webhookSecret)
    .update(stringToSign)
    .digest('hex');

  // 5. 比较签名
  try {
    const signaturesMatch = crypto.timingSafeEqual(
      Buffer.from(receivedSignature, 'hex'),
      Buffer.from(expectedSignature, 'hex')
    );

    if (signaturesMatch) {
      console.log('Webhook validated successfully!');
      // 在这里处理你的业务逻辑，例如：
      // const eventData = JSON.parse(rawBody);
      // handleEvent(eventData);
      res.status(200).json({ received: true });
    } else {
      throw new Error('Signatures do not match.');
    }
  } catch (error) {
    console.error('Webhook signature verification failed:', error.message);
    return res.status(403).send('Invalid signature');
  }
});

const PORT = process.env.PORT || 3000;
app.listen(PORT, () => {
  console.log(`Server listening on port ${PORT}`);
});
```
