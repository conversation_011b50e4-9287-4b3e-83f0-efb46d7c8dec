import { createFileRoute } from '@tanstack/react-router'
import { useState } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Textarea } from '@/components/ui/textarea'
import { trpc } from '@/lib/trpc'
import { CreditCard, Coins, Plus, Smartphone } from 'lucide-react'
import { toast } from 'sonner'

export const Route = createFileRoute('/_authenticated/recharge')({
  component: RechargePage,
})

function RechargePage() {
  const [selectedAppId, setSelectedAppId] = useState<string>('')
  const [amount, setAmount] = useState('')
  const [type, setType] = useState<'PURCHASE' | 'GIFT'>('GIFT')
  const [remarks, setRemarks] = useState('')

  // 获取用户的应用列表
  const { data: appsData } = trpc.application.list.useQuery({
    page: 1,
    pageSize: 100,
  })

  // 获取选中应用的余额
  const { data: balanceData, refetch: refetchBalance } = trpc.balance.getApplicationBalance.useQuery(
    { applicationId: selectedAppId },
    { enabled: !!selectedAppId }
  )

  // 管理员充值mutation
  const rechargeMutation = trpc.order.adminRecharge.useMutation({
    onSuccess: (data) => {
      toast.success(`充值成功！订单号：${data.orderNo}`)
      setAmount('')
      setRemarks('')
      refetchBalance()
    },
    onError: (error) => {
      toast.error(error.message)
    },
  })

  const handleRecharge = async () => {
    if (!selectedAppId) {
      toast.error('请选择应用')
      return
    }

    if (!amount || parseFloat(amount) <= 0) {
      toast.error('请输入有效的充值金额')
      return
    }

    rechargeMutation.mutate({
      applicationId: selectedAppId,
      amount: parseFloat(amount),
      type,
      remarks,
    })
  }

  const selectedApp = appsData?.items?.find((app) => app.id === selectedAppId)

  return (
    <div className="container mx-auto p-6 space-y-6">
      <div className="flex items-center gap-2">
        <CreditCard className="h-6 w-6" />
        <h1 className="text-2xl font-bold">充值测试</h1>
      </div>

      {/* 应用选择 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Smartphone className="h-5 w-5" />
            选择应用
          </CardTitle>
          <CardDescription>请先选择要充值的应用</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="w-full max-w-sm flex gap-2 items-center">
            <Label htmlFor="app-select">应用</Label>
            <Select value={selectedAppId} onValueChange={setSelectedAppId}>
              <SelectTrigger>
                <SelectValue placeholder="请选择应用" />
              </SelectTrigger>
              <SelectContent>
                {appsData?.items?.map((app) => (
                  <SelectItem key={app.id} value={app.id}>
                    {app.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      {selectedAppId && (
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* 当前余额 */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Coins className="h-5 w-5" />
                {selectedApp?.name} - 当前余额
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-3xl font-bold text-primary">{balanceData?.balance || '0.00'} 蚁贝</div>
              <p className="text-sm text-muted-foreground mt-2">1元 = 1蚁贝</p>
            </CardContent>
          </Card>

          {/* 充值表单 */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Plus className="h-5 w-5" />
                充值操作
              </CardTitle>
              <CardDescription>为 {selectedApp?.name} 充值蚁贝</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex gap-2 flex-col">
                <Label htmlFor="amount">充值金额（元）</Label>
                <Input
                  id="amount"
                  type="number"
                  placeholder="请输入充值金额"
                  value={amount}
                  onChange={(e) => setAmount(e.target.value)}
                  min="0.01"
                  step="0.01"
                />
                {amount && (
                  <p className="text-sm text-muted-foreground mt-1">
                    将为 {selectedApp?.name} 增加 {parseFloat(amount || '0').toFixed(2)} 蚁贝
                  </p>
                )}
              </div>

              <div className="flex gap-2 flex-col">
                <Label htmlFor="type">充值类型</Label>
                <Select value={type} onValueChange={(value) => setType(value as 'PURCHASE' | 'GIFT')}>
                  <SelectTrigger>
                    <SelectValue placeholder="选择充值类型" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="GIFT">赠送</SelectItem>
                    <SelectItem value="PURCHASE">购买</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="flex gap-2 flex-col">
                <Label htmlFor="remarks">备注说明（可选）</Label>
                <Textarea
                  id="remarks"
                  placeholder="输入备注信息"
                  value={remarks}
                  onChange={(e) => setRemarks(e.target.value)}
                  rows={3}
                />
              </div>

              <Button onClick={handleRecharge} disabled={rechargeMutation.isPending || !amount} className="w-full">
                {rechargeMutation.isPending ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                    充值中...
                  </>
                ) : (
                  <>
                    <Plus className="h-4 w-4 mr-2" />
                    确认充值
                  </>
                )}
              </Button>
            </CardContent>
          </Card>
        </div>
      )}

      {/* 消费规则说明 */}
      <Card>
        <CardHeader>
          <CardTitle>消费规则</CardTitle>
          <CardDescription>了解蚁贝的使用规则（应用级别）</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="p-4 border rounded-lg bg-orange-50 dark:bg-orange-950 border-orange-200 dark:border-orange-800">
              <h4 className="font-medium flex items-center gap-2 mb-2">
                <span className="bg-orange-100 dark:bg-orange-800 p-2 rounded-lg">👤</span>
                账号额度（自动扣费）
              </h4>
              <p className="text-sm text-muted-foreground mb-2">1个账号额度 = 40蚁贝</p>
              <p className="text-xs text-orange-600 dark:text-orange-400">API调用时自动从应用余额扣除，无需预购买</p>
            </div>
            <div className="p-4 border rounded-lg bg-blue-50 dark:bg-blue-950 border-blue-200 dark:border-blue-800">
              <h4 className="font-medium flex items-center gap-2 mb-2">
                <span className="bg-blue-100 dark:bg-blue-800 p-2 rounded-lg">📶</span>
                流量使用（自动扣费）
              </h4>
              <p className="text-sm text-muted-foreground mb-2">1GB流量 = 1蚁贝</p>
              <p className="text-xs text-blue-600 dark:text-blue-400">API调用时自动从应用余额扣除，无需预购买</p>
            </div>
          </div>

          <div className="mt-4 p-4 bg-yellow-50 dark:bg-yellow-950 border border-yellow-200 dark:border-yellow-800 rounded-lg">
            <h4 className="font-medium text-yellow-800 dark:text-yellow-200 mb-2">💡 重要提醒</h4>
            <ul className="text-sm text-yellow-700 dark:text-yellow-300 space-y-1">
              <li>• 蚁贝现在属于应用，不同应用的余额独立计算</li>
              <li>• 账号额度和流量作为消耗品，在API调用时自动扣费</li>
              <li>• 应用余额不足时，API调用将失败</li>
              <li>• 可以随时为应用充值蚁贝来增加余额</li>
            </ul>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
