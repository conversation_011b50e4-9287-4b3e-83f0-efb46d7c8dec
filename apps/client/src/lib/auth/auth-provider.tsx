import { useState, useEffect } from 'react'
import type { ReactNode } from 'react'
import { getAuthToken, clearAuthToken } from './auth'
import { router } from '@/router'
import { AuthContext, type AuthContextType, type AuthState } from './auth-context'

export function AuthProvider({ children }: { children: ReactNode }) {
  const [authState, setAuthState] = useState<AuthState>({
    isAuthenticated: false,
    token: null,
    user: null,
    isLoading: true,
  })

  // 初始化认证状态
  useEffect(() => {
    const token = getAuthToken()
    if (token) {
      setAuthState((prev) => ({
        ...prev,
        isAuthenticated: true,
        token,
        isLoading: false,
      }))
    } else {
      setAuthState((prev) => ({
        ...prev,
        isLoading: false,
      }))
    }
  }, [])

  const login = (token: string) => {
    setAuthState({
      isAuthenticated: true,
      token,
      user: null, // 可以在这里解析 token 获取用户信息
      isLoading: false,
    })
  }

  const logout = () => {
    clearAuthToken()
    setAuthState({
      isAuthenticated: false,
      token: null,
      user: null,
      isLoading: false,
    })
    router.navigate({ to: '/login' })
  }

  const refresh = async () => {
    // 这里可以添加刷新 token 的逻辑
    const token = getAuthToken()
    if (token) {
      setAuthState((prev) => ({
        ...prev,
        isAuthenticated: true,
        token,
      }))
    }
  }

  const contextValue: AuthContextType = {
    ...authState,
    login,
    logout,
    refresh,
  }

  return <AuthContext.Provider value={contextValue}>{children}</AuthContext.Provider>
}
