const AUTH_TOKEN_KEY = 'auth-token'

/**
 * 保存认证 token 到 localStorage
 */
export function saveAuthToken(token: string): void {
  localStorage.setItem(AUTH_TOKEN_KEY, token)
}

/**
 * 从 localStorage 获取认证 token
 */
export function getAuthToken(): string | null {
  return localStorage.getItem(AUTH_TOKEN_KEY)
}

/**
 * 从 localStorage 清除认证 token
 */
export function clearAuthToken(): void {
  localStorage.removeItem(AUTH_TOKEN_KEY)
}

/**
 * 检查用户是否已登录
 */
export function isAuthenticated(): boolean {
  return !!getAuthToken()
}

/**
 * 登录成功后的处理
 * 保存 token 并可选择性地重定向
 */
export function handleLoginSuccess(token: string, redirectUrl?: string): void {
  saveAuthToken(token)
  
  if (redirectUrl) {
    window.location.href = redirectUrl
  }
}

/**
 * 退出登录的处理
 * 清除 token 并可选择性地重定向到登录页
 */
export function handleLogout(redirectUrl?: string): void {
  clearAuthToken()
  
  if (redirectUrl) {
    window.location.href = redirectUrl
  }
} 