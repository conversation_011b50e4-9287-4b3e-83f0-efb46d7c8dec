import type { Application } from '@coozf/db/schema'
import { TRPCError } from '@trpc/server'
import { LRUCache } from 'lru-cache'
import z from 'zod'
import { protectedProcedure } from '.'

const appPermissionCache = new LRUCache<string, Application>({
  max: 5000,
  ttl: 5 * 60 * 1000, // 5分钟权限缓存
})

export const applicationProcedure = protectedProcedure
  .input(
    z.object({
      applicationId: z.string().uuid(),
    })
  )
  .use(async function isAuthApp({ ctx, next, input }) {
    const { applicationId } = input
    const cacheKey = `${ctx.user.id}:${applicationId}`

    // 检查权限缓存
    if (!appPermissionCache.has(cacheKey)) {
      // 缓存未命中，查询数据库验证权限
      const existingApp = await ctx.appRepo.findByUserIdAndId(ctx.user.id, applicationId)

      if (!existingApp) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: '应用不存在或无权限访问',
        })
      }

      // 缓存权限验证结果
      appPermissionCache.set(cacheKey, existingApp)
    }

    return next({
      ctx: {
        ...ctx,
        applicationId,
        application: appPermissionCache.get(cacheKey)!,
      },
    })
  })

export const applicationWithBalanceProcedure = protectedProcedure
  .input(
    z.object({
      applicationId: z.string().uuid(),
    })
  )
  .use(async function isAuthApp({ ctx, next, input }) {
    const { applicationId } = input

    // 验证权限并获取应用数据
    const application = await ctx.appRepo.findByUserIdAndId(ctx.user.id, applicationId)

    if (!application) {
      throw new TRPCError({
        code: 'NOT_FOUND',
        message: '应用不存在或无权限访问',
      })
    }

    // 获取余额信息
    const balance = await ctx.balanceRepo.findByApplicationId(applicationId)

    const applicationWithBalance = {
      ...application,
      balance: balance?.balance?.toString() || '0.00',
    }

    return next({
      ctx: {
        ...ctx,
        applicationId,
        applicationWithBalance,
      },
    })
  })
