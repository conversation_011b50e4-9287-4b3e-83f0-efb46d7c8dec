import { users } from '@coozf/db/schema'
import type { User } from '@coozf/db/schema'
import { verifyToken } from '@/lib/jwt'
import { t } from '@/trpc'
import { TRPCError } from '@trpc/server'
import { eq } from '@coozf/db'
import { LRUCache } from 'lru-cache'
// 缓存配置
const userCache = new LRUCache<string, User>({
  max: 1000,
  ttl: 5 * 60 * 1000, // 5分钟用户信息缓存
})

export const authMiddleware = t.middleware(async ({ ctx, next }) => {
  // 从cookie或Authorization header获取accessToken
  const accessToken =
    ctx.req.cookies['auth-token'] ??
    (ctx.req.headers.authorization?.startsWith('Bearer ') ? ctx.req.headers.authorization.slice(7) : undefined)

  if (!accessToken) {
    throw new TRPCError({
      code: 'UNAUTHORIZED',
      message: '请先登录或提供有效的访问令牌',
    })
  }

  try {
    const payload = verifyToken(accessToken)

    // 检查用户缓存
    let dbUser = userCache.get(payload.userId)

    if (!dbUser) {
      // 缓存未命中，查询数据库
      dbUser = await ctx.db.query.users.findFirst({
        where: eq(users.id, payload.userId),
      })

      if (!dbUser) {
        throw new TRPCError({
          code: 'UNAUTHORIZED',
          message: '用户不存在',
        })
      }

      // 缓存用户信息
      userCache.set(payload.userId, dbUser)
    }

    return next({
      ctx: {
        ...ctx,
        user: dbUser,
      },
    })
  } catch (error) {
    if (error instanceof TRPCError) {
      throw error
    }

    throw new TRPCError({
      code: 'UNAUTHORIZED',
      message: 'Token无效或已过期',
    })
  }
})

export const protectedProcedure = t.procedure.use(authMiddleware)
