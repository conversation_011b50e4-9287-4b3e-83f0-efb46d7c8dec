import { db } from '@coozf/db/client'
import type { User, Application } from '@coozf/db/schema'
import { ApplicationRepository, UserRepository } from '@coozf/db'
import { verifyOpenAPIToken } from '@/lib/jwt'
import { t } from '@/trpc'
import { TRPCError } from '@trpc/server'
import { LRUCache } from 'lru-cache'

// 创建 Repository 实例
const appRepo = new ApplicationRepository(db)
const userRepo = new UserRepository(db)

// 缓存用户-应用组合数据
type UserAppData = {
  user: User
  application: Application
}

const userAppCache = new LRUCache<string, UserAppData>({
  max: 1000,
  ttl: 5 * 60 * 1000, // 5分钟缓存
})

// 验证token是否有效
export async function verifyToken(authHeader: string) {
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    throw new TRPCError({
      code: 'UNAUTHORIZED',
      message: '请提供有效的 Authorization header',
    })
  }

  const token = authHeader.slice(7)

  try {
    const payload = verifyOpenAPIToken(token)
    const cacheKey = `${payload.userId}:${payload.appId}`

    let userAppData = userAppCache.get(cacheKey)

    if (!userAppData) {
      // 查找应用并验证权限
      const applications = await appRepo.findMany({
        where: { appId: payload.appId },
        take: 1,
        include: { user: true }
      })

      if (applications.length === 0) {
        throw new TRPCError({
          code: 'UNAUTHORIZED',
          message: '应用或用户不存在',
        })
      }

      const application = applications[0]
      const user = await userRepo.findById(application.userId)

      if (!user) {
        throw new TRPCError({
          code: 'UNAUTHORIZED',
          message: '用户不存在',
        })
      }

      userAppData = {
        user,
        application,
      }

      userAppCache.set(cacheKey, userAppData)
    }

    return userAppData
  } catch {
    throw new TRPCError({
      code: 'UNAUTHORIZED',
      message: 'Token 无效或已过期',
    })
  }
}

export const openAPIMiddleware = t.middleware(async ({ ctx, next }) => {
  const authHeader = ctx.req.headers.authorization

  const userAppData = await verifyToken(authHeader ?? '')

  return next({
    ctx: {
      ...ctx,
      user: userAppData.user,
      application: userAppData.application,
    },
  })
})

export const openAPIProcedure = t.procedure.use(openAPIMiddleware)
