import type { CreateFastifyContextOptions } from '@trpc/server/adapters/fastify'
import { db } from '@coozf/db/client'
import type { User } from '@coozf/db/schema'
import {
  UserRepository,
  ApplicationRepository,
  OrderRepository,
  ApplicationBalanceRepository,
  TransactionRepository,
  ApiCallRepository,
  AuthAccountRepository,
} from '@coozf/db'

// 创建 Repository 实例
const userRepo = new UserRepository(db)
const appRepo = new ApplicationRepository(db)
const orderRepo = new OrderRepository(db)
const balanceRepo = new ApplicationBalanceRepository(db)
const transactionRepo = new TransactionRepository(db)
const apiCallRepo = new ApiCallRepository(db)
const authAccountRepo = new AuthAccountRepository(db)

const createContext = async ({ req, res }: CreateFastifyContextOptions) => {
  const user: User | null = null
  return {
    req,
    res,
    db,
    user,
    isPublicApi: false,
    userRepo,
    appRepo,
    orderRepo,
    balanceRepo,
    transactionRepo,
    apiCallRepo,
    authAccountRepo,
  }
}

export type Context = Awaited<ReturnType<typeof createContext>> & {
  user: User | null
  isPublicApi?: boolean
}

export default createContext
