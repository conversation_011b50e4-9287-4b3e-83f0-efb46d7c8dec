import type { CreateFastifyContextOptions } from '@trpc/server/adapters/fastify'
import { db } from '@coozf/db/client'
import type { User } from '@coozf/db/schema'

const createContext = async ({ req, res }: CreateFastifyContextOptions) => {
  const user: User | null = null
  return { req, res, db, user, isPublicApi: false }
}

export type Context = Awaited<ReturnType<typeof createContext>> & {
  user: User | null
  isPublicApi?: boolean
}

export default createContext

