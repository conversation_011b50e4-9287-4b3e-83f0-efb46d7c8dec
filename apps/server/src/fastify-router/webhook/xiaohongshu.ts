import type { FastifyInstance, FastifyRequest, FastifyReply } from 'fastify'
import { eq, and } from '@coozf/db'
import { authAccounts, applications } from '@coozf/db/schema'
import { decrypt } from '@/lib/crypto'
import { sendWebhookNotification } from '@/lib/oauth2'
import { db } from '@coozf/db/client'
import { env } from '@/env'
import type {
  XiaohongshuMessageWebhookBody,
  XiaohongshuPushLeadWebhookBody,
  XiaohongshuBindAccountDecryptedData,
  XiaohongshuUnbindAccountDecryptedData,
  XiaohongshuBindUserDecryptedData,
  XiaohongshuWebhookBaseBody,
} from './types/xiaohongshu'

// 转发给应用的标准数据格式
interface ForwardedWebhookData {
  platform: string
  event: string
  original_data: unknown
  user_info: {
    platform_user_id?: string
    user_id?: string
    account_id?: string
  }
  timestamp: number
  [key: string]: unknown
}

// Webhook事件类型配置
interface WebhookEventConfig {
  eventType: string
  logIcon: string
  description: string
}

// 支持的webhook事件类型
const WEBHOOK_EVENTS: Record<string, WebhookEventConfig> = {
  bind_account: {
    eventType: 'bind_account',
    logIcon: '🔗',
    description: '账号绑定',
  },
  unbind_account: {
    eventType: 'unbind_account',
    logIcon: '🔓',
    description: '账号解绑',
  },
  bind_user: {
    eventType: 'bind_user',
    logIcon: '👤',
    description: '用户绑定',
  },
  message: {
    eventType: 'message',
    logIcon: '💬',
    description: '消息接收',
  },
  push_lead: {
    eventType: 'push_lead',
    logIcon: '📊',
    description: '留资推送',
  },
}

/**
 * 查找关联的应用
 */
async function findApplicationByUserId(platformUserId: string) {
  try {
    const result = await db
      .select()
      .from(authAccounts)
      .innerJoin(applications, eq(authAccounts.appId, applications.id))
      .where(and(eq(authAccounts.platform, 'xiaohongshu'), eq(authAccounts.platformUserId, platformUserId)))
      .limit(1)

    if (!result[0]) {
      return null
    }

    const row = result[0]
    return row
  } catch (error) {
    console.error('查找应用失败:', error)
    return null
  }
}

/**
 * 解密和解析小红书数据
 */
function decryptAndParseData(body: XiaohongshuWebhookBaseBody): unknown {
  if (body.content) {
    try {
      // 使用环境变量中的密钥解密
      const decryptedJson = decrypt(body.content, env.XIAOHONGSHU_SECRET)
      return JSON.parse(decryptedJson)
    } catch (error) {
      console.error('小红书数据解密失败:', error)
      throw new Error('数据解密失败')
    }
  }
  return body
}

/**
 * 转发webhook到应用
 */
async function forwardWebhookToApplication(
  webhookUrl: string,
  webhookSecret: string,
  data: ForwardedWebhookData,
): Promise<boolean> {
  try {
    return await sendWebhookNotification(webhookUrl, webhookSecret, 'xiaohongshu', data.original_data, data.event)
  } catch (error) {
    console.error('转发webhook失败:', error)
    return false
  }
}

/**
 * 处理需要解密的 webhook 事件（bind_account, unbind_account, bind_user）
 */
async function handleEncryptedWebhook(request: FastifyRequest, reply: FastifyReply, eventConfig: WebhookEventConfig) {
  console.log(`${eventConfig.logIcon} 收到小红书${eventConfig.description}webhook:`, request.body)

  try {
    const body = request.body as XiaohongshuWebhookBaseBody
    const decryptedData = decryptAndParseData(body)

    let platformUserId = ''

    // 根据解密后的数据获取用户ID
    if (eventConfig.eventType === 'bind_account') {
      const data = decryptedData as XiaohongshuBindAccountDecryptedData
      platformUserId = data.user_id
    } else if (eventConfig.eventType === 'unbind_account') {
      const data = decryptedData as XiaohongshuUnbindAccountDecryptedData
      platformUserId = data.user_id
    } else if (eventConfig.eventType === 'bind_user') {
      const data = decryptedData as XiaohongshuBindUserDecryptedData
      platformUserId = data.kos_user_id || data.user_id
    }

    if (!platformUserId) {
      return reply.status(400).send({
        success: false,
        message: '缺少用户ID信息',
      })
    }

    // 查找关联应用
    const appInfo = await findApplicationByUserId(platformUserId)
    if (!appInfo?.applications.webhookUrl) {
      console.warn(`未找到用户 ${platformUserId} 关联的应用或webhook地址`)
      return reply.send({ success: true, message: '未找到关联应用' })
    }

    // 转发到应用
    const forwardData: ForwardedWebhookData = {
      platform: 'xiaohongshu',
      event: eventConfig.eventType,
      original_data: decryptedData,
      user_info: {
        platform_user_id: platformUserId,
      },
      timestamp: Date.now(),
    }

    const success = await forwardWebhookToApplication(
      appInfo.applications.webhookUrl,
      appInfo.applications.webhookSecret,
      forwardData,
    )

    if (success) {
      console.log(`✅ 成功转发${eventConfig.description}webhook到应用: ${appInfo.applications.name}`)
    } else {
      console.warn(`❌ 转发${eventConfig.description}webhook失败: ${appInfo.applications.name}`)
    }

    return reply.send({ success: true })
  } catch (error) {
    console.error(`处理小红书${eventConfig.description}webhook失败:`, error)
    return reply.status(500).send({
      success: false,
      message: '内部服务器错误',
    })
  }
}

/**
 * 处理消息 webhook 事件
 */
async function handleMessageWebhook(request: FastifyRequest, reply: FastifyReply, eventConfig: WebhookEventConfig) {
  console.log(`${eventConfig.logIcon} 收到小红书${eventConfig.description}webhook:`, request.body)

  try {
    const body = request.body as XiaohongshuMessageWebhookBody
    const platformUserId = body.message_source === 3 ? body.from_user_id : body.to_user_id

    if (!platformUserId) {
      return reply.status(400).send({
        success: false,
        message: '缺少用户ID信息',
      })
    }

    // 查找关联应用
    const appInfo = await findApplicationByUserId(platformUserId)
    if (!appInfo?.applications.webhookUrl) {
      console.warn(`未找到用户 ${platformUserId} 关联的应用或webhook地址`)
      return reply.send({ success: true, message: '未找到关联应用' })
    }

    // 解密content
    const decryptedData = decryptAndParseData(body)
    body.content = decryptedData as string

    // 转发到应用
    const forwardData: ForwardedWebhookData = {
      platform: 'xiaohongshu',
      event: eventConfig.eventType,
      original_data: body,
      user_info: {
        platform_user_id: platformUserId,
        user_id: body.from_user_id,
      },
      timestamp: Date.now(),
    }

    const success = await forwardWebhookToApplication(
      appInfo.applications.webhookUrl,
      appInfo.applications.webhookSecret,
      forwardData,
    )

    if (success) {
      console.log(`✅ 成功转发${eventConfig.description}webhook到应用: ${appInfo.applications.name}`)
    } else {
      console.warn(`❌ 转发${eventConfig.description}webhook失败: ${appInfo.applications.name}`)
    }

    return reply.send({ success: true })
  } catch (error) {
    console.error(`处理小红书${eventConfig.description}webhook失败:`, error)
    return reply.status(500).send({
      success: false,
      message: '内部服务器错误',
    })
  }
}

/**
 * 处理留资推送 webhook 事件
 */
async function handlePushLeadWebhook(request: FastifyRequest, reply: FastifyReply, eventConfig: WebhookEventConfig) {
  console.log(`${eventConfig.logIcon} 收到小红书${eventConfig.description}webhook:`, request.body)

  try {
    const body = request.body as XiaohongshuPushLeadWebhookBody
    const platformUserId = body.brand_user_id || body.kos_user_id

    if (!platformUserId) {
      return reply.status(400).send({
        success: false,
        message: '缺少用户ID信息',
      })
    }

    // 查找关联应用
    const appInfo = await findApplicationByUserId(platformUserId)
    if (!appInfo?.applications.webhookUrl) {
      console.warn(`未找到用户 ${platformUserId} 关联的应用或webhook地址`)
      return reply.send({ success: true, message: '未找到关联应用' })
    }

    // 转发到应用
    const forwardData: ForwardedWebhookData = {
      platform: 'xiaohongshu',
      event: eventConfig.eventType,
      original_data: body,
      user_info: {
        platform_user_id: platformUserId,
        user_id: body.user_id,
      },
      timestamp: Date.now(),
    }

    const success = await forwardWebhookToApplication(
      appInfo.applications.webhookUrl,
      appInfo.applications.webhookSecret,
      forwardData,
    )

    if (success) {
      console.log(`✅ 成功转发${eventConfig.description}webhook到应用: ${appInfo.applications.name}`)
    } else {
      console.warn(`❌ 转发${eventConfig.description}webhook失败: ${appInfo.applications.name}`)
    }

    return reply.send({ success: true })
  } catch (error) {
    console.error(`处理小红书${eventConfig.description}webhook失败:`, error)
    return reply.status(500).send({
      success: false,
      message: '内部服务器错误',
    })
  }
}

/**
 * 创建小红书webhook路由
 */
function createXiaohongshuWebhookRoute(app: FastifyInstance, path: string, eventKey: string) {
  const eventConfig = WEBHOOK_EVENTS[eventKey]
  if (!eventConfig) {
    throw new Error(`未知的webhook事件类型: ${eventKey}`)
  }

  app.post(path, async (request, reply) => {
    // 根据事件类型选择合适的处理函数
    switch (eventKey) {
      case 'bind_account':
      case 'unbind_account':
      case 'bind_user':
        return handleEncryptedWebhook(request, reply, eventConfig)
      case 'message':
        return handleMessageWebhook(request, reply, eventConfig)
      case 'push_lead':
        return handlePushLeadWebhook(request, reply, eventConfig)
      default:
        return reply.status(400).send({
          success: false,
          message: `不支持的事件类型: ${eventKey}`,
        })
    }
  })
}

/**
 * 小红书Webhook路由注册
 * bindAccount: '/open/im/third/bind_account',      // 账号绑定
  unbindAccount: '/open/im/third/unbind_account',  // 账号解绑
  bindUser: '/open/im/auth/bind_user/event',       // 用户绑定
  message: '/open/im/send',                        // 消息接收
  pushLead: '/open/im/push_lead' 
 */
export async function xiaohongshuWebhookRoutes(app: FastifyInstance) {
  // 注册各个webhook端点
  createXiaohongshuWebhookRoute(app, '/open/im/third/bind_account', 'bind_account')
  createXiaohongshuWebhookRoute(app, '/open/im/third/unbind_account', 'unbind_account')
  createXiaohongshuWebhookRoute(app, '/open/im/auth/bind_user/event', 'bind_user')
  createXiaohongshuWebhookRoute(app, '/open/im/send', 'message')
  createXiaohongshuWebhookRoute(app, '/open/im/push_lead', 'push_lead')

  // webhook健康检查
  app.get('/health', async (_, reply) => {
    return reply.send({
      success: true,
      message: '小红书webhook服务正常',
      timestamp: Date.now(),
      platform: 'xiaohongshu',
      supported_events: Object.keys(WEBHOOK_EVENTS),
    })
  })
}
