// 小红书webhook消息类型
// {
//     user_info: [
//       {
//         user_id: '88aef715b57e25f282e40d970100016b',
//         nickname: 'jalousi',
//         header_image: 'https://sns-avatar-qc.xhscdn.com/avatar/5dc59d210c73f50001b17518.jpg?imageView2/2/w/120/format/jpg'
//       }
//     ],
//     to_user_id: '88aef715b57e25f282e40d970100016b',
//     message_type: 'TEXT',
//     message_id: '41e8afe271383aa9b513c263e503f7cd_1000c5c2',
//     content: 'bL/5PEzuBtl7qlfynrFdaw==~split~JvO7s9SvVMW3mVv067zgGyCaDj1CmSTXuR0Go6wMaGY=',
//     message_source: 3,
//     timestamp: '1751273721815',
//     from_user_id: '61b8073f000000001000c5c2'
//   }

// ================ 基础数据类型 ================

// C端用户信息
export interface XiaohongshuUserInfo {
  user_id: string
  nickname: string
  header_image: string
}

// 通用响应格式
export interface XiaohongshuWebhookResponse {
  code: number
  msg?: string
  success: boolean
}

// ================ Webhook 请求体类型 ================

// 通用的webhook请求体基础结构
export interface XiaohongshuWebhookBaseBody {
  content: string  // 加密后的内容
}

// 绑定账户 webhook 请求体 (content 解密后包含: user_id, nick_name, token)
export type XiaohongshuBindAccountWebhookBody = XiaohongshuWebhookBaseBody

// 解绑账户 webhook 请求体 (content 解密后包含: user_id, app_id, account_code)
export type XiaohongshuUnbindAccountWebhookBody = XiaohongshuWebhookBaseBody

// 绑定用户(KOS) webhook 请求体 (content 解密后包含: user_id, auth_status, kos_nick_name, kos_user_id)
export type XiaohongshuBindUserWebhookBody = XiaohongshuWebhookBaseBody

// 消息 webhook 请求体
export interface XiaohongshuMessageWebhookBody {
  message_id: string
  message_type: string  // TEXT, IMAGE, VIDEO, CARD, HINT, REVOKE, SMILES
  message_source: number  // 1: C2B用户消息, 2: C2B系统消息, 3: B2C系统消息
  from_user_id: string
  to_user_id: string
  timestamp: number
  content: string  // 消息内容，可能是JSON字符串
  user_info: XiaohongshuUserInfo[]
}

// 留资推送 webhook 请求体
export interface XiaohongshuPushLeadWebhookBody {
  user_id: string  // C端用户ID
  brand_user_id: string  // 广告主用户ID
  kos_user_id?: string  // KOS用户ID
  conv_time?: string  // 归因时间
  advertiser_name?: string  // 广告主名称
  advertiser_id?: string  // 广告主ID
  campaign_name?: string  // 计划名称
  campaign_id?: string  // 计划ID
  creativity_name?: string  // 创意名称
  creativity_id?: string  // 创意ID
  leads_tag?: string  // 线索标签
  area?: string  // 省份地区
  phone_num?: string  // 手机号（加密）
  wechat?: string  // 微信号（加密）
  remark?: string  // 备注
  push_type: number  // 1:进线归因, 2:开口归因, 3:留资, 4:留资归因
  wechat_copy?: number  // 是否个人微信复制 0-否 1-是
  link_id?: string  // 企微链接ID
  link_name?: string  // 企微链接名称
  customer_channel?: string  // 企微链接customer_channel
  msg_app_open?: number  // 交易卡是否唤端成功 0-否 1-是
  wechat_type?: number  // 微信号类型 1-文本 2-图片链接
}

// ================ 解密后的数据类型 ================

// 绑定账户解密数据
export interface XiaohongshuBindAccountDecryptedData {
  user_id: string  // 开放平台鉴权id
  nick_name: string  // 用户昵称
  token: string  // 鉴权token
}

// 解绑账户解密数据
export interface XiaohongshuUnbindAccountDecryptedData {
  user_id: string  // 开放平台鉴权id
  app_id: number  // 开放平台应用ID
  account_code: string  // 广告主在三方的注册账号编码
}

// 绑定用户(KOS)解密数据
export interface XiaohongshuBindUserDecryptedData {
  user_id: string  // 开放平台鉴权id
  auth_status: number  // 绑定状态 2-已生效 4-已取消
  kos_nick_name: string  // KOS用户昵称
  kos_user_id: string  // KOS用户ID
}

// 留资回传数据
export interface XiaohongshuLeadBackData {
  third_back_source: string  // 三方标识
  c_user_info: XiaohongshuUserInfo  // C端用户信息
  b_user_info: XiaohongshuUserInfo  // B端用户信息
  time: number  // 客资更新时间
  customer_service_name: string  // 标注客服姓名
  area?: string  // 省份地区
  phone_num?: string  // 电话号码
  wechat?: string  // 微信号
  remark?: string  // 备注信息
  other_fields?: Record<string, unknown>  // 其他自定义字段
}

// ================ 消息内容类型 ================

// 文本消息内容
export interface XiaohongshuTextMessageContent {
  text: string
}

// 图片消息内容
export interface XiaohongshuImageMessageContent {
  image_url: string
  width?: number
  height?: number
}

// 视频消息内容
export interface XiaohongshuVideoMessageContent {
  video_url: string
  cover_url?: string
  duration?: number
}

// 卡片消息内容
export interface XiaohongshuCardMessageContent {
  card_type: string  // note, page, service, business_card, lead_card, trade_card
  card_data: Record<string, unknown>
}

// 撤回消息内容
export interface XiaohongshuRevokeMessageContent {
  message_id: string  // 被撤回的消息ID
}

// Hint消息内容
export interface XiaohongshuHintMessageContent {
  hint_type: string
  hint_text: string
}

// 自定义表情消息内容
export interface XiaohongshusmilesMessageContent {
  smiles_url: string  // 表情图片URL
}

// ================ 统一的 webhook 消息类型 ================

export interface XiaohongshuWebhookMessage {
  user_info: XiaohongshuUserInfo[]
  to_user_id: string
  message_type: string
  message_id: string
  content: string
  message_source: number
  timestamp: string
  from_user_id: string
}

// ================ 类型联合 ================

// 所有webhook请求体类型的联合
export type XiaohongshuWebhookBody = 
  | XiaohongshuBindAccountWebhookBody
  | XiaohongshuUnbindAccountWebhookBody  
  | XiaohongshuBindUserWebhookBody
  | XiaohongshuMessageWebhookBody
  | XiaohongshuPushLeadWebhookBody

// 所有解密数据类型的联合
export type XiaohongshuDecryptedData =
  | XiaohongshuBindAccountDecryptedData
  | XiaohongshuUnbindAccountDecryptedData
  | XiaohongshuBindUserDecryptedData
  | XiaohongshuLeadBackData

// 所有消息内容类型的联合
export type XiaohongshuMessageContent =
  | XiaohongshuTextMessageContent
  | XiaohongshuImageMessageContent
  | XiaohongshuVideoMessageContent
  | XiaohongshuCardMessageContent
  | XiaohongshuRevokeMessageContent
  | XiaohongshuHintMessageContent
  | XiaohongshusmilesMessageContent

// ================ 枚举类型 ================

// 消息类型枚举
export enum XiaohongshuMessageType {
  TEXT = 'TEXT',
  IMAGE = 'IMAGE', 
  VIDEO = 'VIDEO',
  CARD = 'CARD',
  HINT = 'HINT',
  REVOKE = 'REVOKE',
  SMILES = 'SMILES'
}

// 消息来源枚举
export enum XiaohongshuMessageSource {
  C2B_USER = 1,    // C2B 用户消息
  C2B_SYSTEM = 2,  // C2B 系统消息
  B2C_SYSTEM = 3   // B2C 系统消息
}

// 推送类型枚举
export enum XiaohongshuPushType {
  ATTRIBUTION_ENTRY = 1,    // 进线归因后推送
  ATTRIBUTION_OPENING = 2,  // 开口归因后推送
  LEAD_GENERATED = 3,       // 留资后推送
  LEAD_ATTRIBUTION = 4      // 留资归因后推送
}

// KOS授权状态枚举
export enum XiaohongshuAuthStatus {
  ACTIVE = 2,    // 已生效
  CANCELLED = 4  // 已取消
}
