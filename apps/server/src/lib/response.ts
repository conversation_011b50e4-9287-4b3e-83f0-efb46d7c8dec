
/**
 * @/lib/response.ts
 * @description API 响应封装
 */

// 定义标准 API 响应结构
export interface ApiResponse<T> {
  code: number;
  message: string;
  data: T | null;
}

// 定义业务错误类
export class ApiError extends Error {
  public code: number;

  constructor(code: number, message: string) {
    super(message);
    this.code = code;
    this.name = 'ApiError';
  }
}

// 响应封装器
export class ResponseWrapper {
  /**
   * 创建一个成功的 API 响应
   * @param data 响应数据
   * @param message 成功消息，默认为 "Success"
   * @returns {ApiResponse<T>}
   */
  public static success<T>(data: T, message = 'Success'): ApiResponse<T> {
    return {
      code: 0,
      message,
      data,
    };
  }

  /**
   * 创建一个失败的 API 响应
   * @param code 错误码
   * @param message 错误消息
   * @returns {ApiResponse<null>}
   */
  public static error(code: number, message: string): ApiResponse<null> {
    return {
      code,
      message,
      data: null,
    };
  }
}

/**
 * ---- 使用示例 ----
 *
 * // 1. 在 tRPC Procedure 中
 *
 * import { publicProcedure } from '../../trpc';
 * import { ResponseWrapper, ApiError } from '../lib/response';
 *
 * export const userProcedure = {
 *   getUserById: publicProcedure
 *     .input(z.object({ id: z.string() }))
 *     .query(async ({ input }) => {
 *       try {
 *         const user = await findUserById(input.id);
 *         if (!user) {
 *           // 业务错误，例如用户未找到
 *           throw new ApiError(40401, 'User not found');
 *         }
 *         // 返回成功响应
 *         return ResponseWrapper.success(user);
 *       } catch (error) {
 *         if (error instanceof ApiError) {
 *           // 捕获并返回标准错误响应
 *           return ResponseWrapper.error(error.code, error.message);
 *         }
 *         // 对于未知错误，可以记录日志并返回一个通用的服务器错误
 *         console.error('Unhandled error:', error);
 *         return ResponseWrapper.error(50000, 'Internal Server Error');
 *       }
 *     }),
 * };
 *
 *
 * // 2. 在 Fastify Route 中
 *
 * import { FastifyInstance } from 'fastify';
 * import { ResponseWrapper } from '../lib/response';
 *
 * export default async function (fastify: FastifyInstance) {
 *   fastify.get('/health', async (request, reply) => {
 *     // 直接返回成功响应
 *     return ResponseWrapper.success({ status: 'ok' });
 *   });
 *
 *   fastify.get('/some-resource/:id', async (request, reply) => {
 *      const { id } = request.params as { id: string };
 *      if (id !== 'valid-id') {
 *        // 返回错误响应，并设置 HTTP 状态码
 *        reply.status(404);
 *        return ResponseWrapper.error(40401, 'Resource not found');
 *      }
 *
 *      return ResponseWrapper.success({ id, data: 'some data' });
 *   });
 * }
 *
 */
