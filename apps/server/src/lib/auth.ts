import bcrypt from 'bcryptjs'
import { db } from '@coozf/db/client'
import { UserRepository } from '@coozf/db'
import type { User } from '@coozf/db/schema'
import type { JWTPayload } from './jwt'
import { generateToken } from './jwt'
import { VerificationCodeManager } from './verification'
import { isdev } from '../env'

// 创建 Repository 实例
const userRepo = new UserRepository(db)

// 不返回密码的用户类型
export type SafeUser = Omit<User, 'password'>

/**
 * 生成默认头像URL
 */
function generateAvatarUrl(userId: string): string {
  // 使用 DiceBear API 生成头像
  const style = 'avataaars' // 可选风格：avataaars, bottts, pixel-art 等
  const encodedSeed = encodeURIComponent(userId)
  return `https://api.dicebear.com/7.x/${style}/svg?seed=${encodedSeed}&backgroundColor=b6e3f4`
}

/**
 * 密码加密
 */
export async function hashPassword(password: string): Promise<string> {
  return bcrypt.hash(password, 12)
}

/**
 * 密码验证
 */
export async function verifyPassword(password: string, hash: string): Promise<boolean> {
  return bcrypt.compare(password, hash)
}

/**
 * 发送短信验证码 (使用Redis存储)
 */
export async function sendSMSCode(
  phone: string,
  type: 'login' | 'register' | 'reset_password' = 'login',
): Promise<{ code: string; success: boolean }> {
  // 检查发送频率限制
  const rateCheck = await VerificationCodeManager.checkSendRate(phone)
  if (!rateCheck.canSend) {
    throw new Error(`请等待${rateCheck.remainingTime}秒后再试`)
  }

  // 生成验证码并存储到Redis
  const code = await VerificationCodeManager.generateCode(phone, type)

  // 开发环境直接返回验证码，生产环境应该发送真实短信
  if (isdev) {
    console.log(`📱 短信验证码发送到 ${phone}: ${code}`)
    return { code, success: true }
  }

  // 生产环境发送真实短信的逻辑
  try {
    // TODO: 集成真实短信服务提供商
    // await sendRealSMS(phone, code)
    return { code: '', success: true }
  } catch (error) {
    console.error('短信发送失败:', error)
    return { code: '', success: false }
  }
}

/**
 * 验证短信验证码 (使用Redis验证)
 */
export async function verifySMSCode(
  phone: string,
  code: string,
  type: 'login' | 'register' | 'reset_password' = 'login',
): Promise<boolean> {
  const result = await VerificationCodeManager.verifyCode(phone, type, code)
  return result.success
}

/**
 * 手机号登录/注册
 */
export async function loginOrRegisterWithPhone(
  phone: string,
  code: string,
): Promise<{ user: SafeUser; token: string; isNewUser: boolean }> {
  // 验证短信验证码
  const isValidCode = await verifySMSCode(phone, code, 'login')
  if (!isValidCode) {
    throw new Error('验证码错误或已过期')
  }

  // 查找用户
  let user = await userRepo.findByPhone(phone)

  let isNewUser = false

  if (!user) {
    // 新用户，自动注册
    user = await userRepo.create({
      phone,
      phoneVerified: true,
      avatar: generateAvatarUrl(phone),
      name: phone, // 默认用手机号作为姓名
    })
    isNewUser = true
  }

  // 去除密码字段
  const { password: _, ...safeUser } = user

  // 生成JWT token
  const jwtPayload: JWTPayload = {
    userId: user.id,
    phone: user.phone ?? '',
  }

  const token = generateToken(jwtPayload)

  return { user: safeUser, token, isNewUser }
}

/**
 * 邮箱/手机号密码登录
 */
export async function loginWithPassword(
  emailOrPhone: string,
  password: string,
): Promise<{ user: SafeUser; token: string }> {
  // 检查是否是邮箱格式
  const isEmail = emailOrPhone.includes('@')

  const user = isEmail
    ? await userRepo.findByEmail(emailOrPhone)
    : await userRepo.findByPhone(emailOrPhone)

  if (!user) {
    throw new Error('用户不存在')
  }

  if (!user.password) {
    throw new Error('该账号未设置密码，请使用手机号验证码登录')
  }

  // 验证密码
  const isValidPassword = await verifyPassword(password, user.password)
  if (!isValidPassword) {
    throw new Error('密码错误')
  }

  // 去除密码字段
  const { password: _, ...safeUser } = user

  // 生成JWT token
  const jwtPayload: JWTPayload = {
    userId: user.id,
    phone: user.phone ?? '',
  }

  const token = generateToken(jwtPayload)

  return { user: safeUser, token }
}
