import { db } from '@coozf/db/client'
import {
  OrderRepository,
  ApplicationBalanceRepository,
  TransactionRepository
} from '@coozf/db'
import { nanoid } from 'nanoid'
import { rechargeBalance } from './balance'

// 创建 Repository 实例
const orderRepo = new OrderRepository(db)
const balanceRepo = new ApplicationBalanceRepository(db)
const transactionRepo = new TransactionRepository(db)

/**
 * 创建充值订单并直接完成充值
 */
export async function createRechargeOrder(
  userId: string,
  applicationId: string, // 必须指定应用
  amount: number,
  type: 'PURCHASE' | 'GIFT' = 'GIFT',
  remarks?: string
): Promise<string> {
  const antCoins = amount // 1元 = 1蚁贝

  // 使用 Prisma 事务
  const result = await db.$transaction(async (tx) => {
    // 创建订单
    const order = await orderRepo.createWithOrderNo({
      antCoins,
      amount,
      type,
      status: 'COMPLETED', // 直接完成
      remarks,
      user: {
        connect: { id: userId }
      },
      application: {
        connect: { id: applicationId }
      }
    })

    // 直接充值到应用余额
    await rechargeBalance(
      applicationId,
      antCoins,
      order.id,
      `${type === 'GIFT' ? '赠送' : '购买'}充值 - 订单号: ${order.orderNo}`
    )

    return order.orderNo
  })

  return result
}

/**
 * 获取订单列表
 */
export async function getOrderList(params: {
  page?: number
  pageSize?: number
  userId?: string
  status?: 'PENDING' | 'COMPLETED' | 'CANCELLED'
  type?: 'PURCHASE' | 'GIFT'
  startDate?: string
  endDate?: string
  search?: string
}) {
  const { page = 1, pageSize = 10, userId, status, type, startDate, endDate, search } = params

  const skip = (page - 1) * pageSize

  // 构建查询条件
  const where: any = {}
  if (userId) where.userId = userId
  if (status) where.status = status
  if (type) where.type = type
  if (search) {
    where.orderNo = {
      contains: search,
      mode: 'insensitive'
    }
  }
  if (startDate || endDate) {
    where.createdAt = {}
    if (startDate) where.createdAt.gte = new Date(startDate)
    if (endDate) where.createdAt.lte = new Date(endDate)
  }

  // 获取总数
  const total = await orderRepo.count(where)

  // 获取订单列表
  const orderList = await orderRepo.findMany({
    where,
    skip,
    take: pageSize,
    orderBy: { createdAt: 'desc' },
    include: {
      user: true,
      application: true,
    },
  })

  return {
    data: orderList,
    total,
    page,
    pageSize,
  }
}

/**
 * 获取订单详情
 */
export async function getOrderById(orderId: string) {
  const order = await orderRepo.findById(orderId)
  return order
}

/**
 * 更新订单状态
 */
export async function updateOrderStatus(
  orderId: string,
  status: 'PENDING' | 'COMPLETED' | 'CANCELLED',
  remarks?: string
): Promise<void> {
  const updateData: any = { status }
  if (remarks) updateData.remarks = remarks

  await orderRepo.update(orderId, updateData)
}

/**
 * 申请发票
 */
export async function requestInvoice(orderId: string): Promise<void> {
  await orderRepo.update(orderId, { invoiceRequested: true })
}

/**
 * 取消订单
 */
export async function cancelOrder(orderId: string): Promise<void> {
  await updateOrderStatus(orderId, 'CANCELLED', '订单已取消')
}

/**
 * 获取用户订单统计
 */
export async function getUserOrderStats(userId: string) {
  // 使用 Repository 的统计方法
  const stats = await orderRepo.getOrderStats({ userId })

  return {
    totalOrders: stats.totalOrders,
    completedOrders: stats.completedOrders,
    totalAmount: stats.totalAmount,
  }
}
