import crypto from 'crypto'
import { db } from '@coozf/db/client'
import { ApplicationRepository } from '@coozf/db'
import { generateSecret as generateSecretCrypto, hashSecret } from '@/lib/crypto'

// 创建 Repository 实例
const appRepo = new ApplicationRepository(db)

/**
 * 生成应用ID
 * 格式：app_ + 16位随机字符串
 */
export function generateAppId(): string {
  const randomString = crypto.randomBytes(8).toString('hex') // 16位十六进制字符
  return `app_${randomString}`
}

/**
 * 生成应用密钥
 * 格式：sk_ + 32位随机字符串 (已废弃，使用 generateAndHashSecret)
 * @deprecated 使用 generateAndHashSecret 替代
 */
export function generateSecret(): string {
  const randomString = crypto.randomBytes(16).toString('hex') // 32位十六进制字符
  return `sk_${randomString}`
}

/**
 * 生成新的 Secret 并返回明文和哈希值
 */
export async function generateAndHashSecret(): Promise<{ secret: string; hashedSecret: string }> {
  const secret = `sk_${generateSecretCrypto()}`
  const hashedSecret = await hashSecret(secret)
  return { secret, hashedSecret }
}

/**
 * 确保生成唯一的应用ID
 * 如果生成的ID已存在，会重试最多5次
 */
export async function generateUniqueAppId(): Promise<string> {
  const maxRetries = 5

  for (let i = 0; i < maxRetries; i++) {
    const appId = generateAppId()

    // 检查是否已存在
    const existing = await appRepo.findMany({
      where: { appId },
      take: 1,
    })

    if (existing.length === 0) {
      return appId
    }
  }

  throw new Error('无法生成唯一的应用ID，请稍后重试')
}

/**
 * 格式化余额显示
 */
export function formatBalance(balance: string | number): string {
  const num = typeof balance === 'string' ? parseFloat(balance) : balance
  return `${num.toFixed(2)} 蚁贝`
}

/**
 * 格式化流量显示
 */
export function formatTraffic(trafficKB: number): string {
  if (trafficKB < 1024) {
    return `${trafficKB} KB`
  } else if (trafficKB < 1024 * 1024) {
    return `${(trafficKB / 1024).toFixed(2)} MB`
  } else {
    return `${(trafficKB / (1024 * 1024)).toFixed(2)} GB`
  }
}
