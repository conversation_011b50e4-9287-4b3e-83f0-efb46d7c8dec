import bcrypt from 'bcrypt'
import { randomBytes, createDecipheriv } from 'crypto'

const SALT_ROUNDS = 12

/**
 * 生成随机 Secret
 */
export function generateSecret(): string {
  // 生成 32 位随机字符串，使用 base64 编码
  return randomBytes(24).toString('base64url') // base64url 避免特殊字符
}

/**
 * 哈希 Secret
 */
export async function hashSecret(secret: string): Promise<string> {
  return bcrypt.hash(secret, SALT_ROUNDS)
}

/**
 * 验证 Secret
 */
export async function verifySecret(secret: string, hashedSecret: string): Promise<boolean> {
  return bcrypt.compare(secret, hashedSecret)
}

/**
 * 格式化 Secret 显示（只显示前4位和后4位）
 */
export function formatSecretForDisplay(secret: string): string {
  if (secret.length < 8) {
    return '*'.repeat(secret.length)
  }
  const start = secret.slice(0, 4)
  const end = secret.slice(-4)
  const middle = '*'.repeat(Math.max(8, secret.length - 8))
  return `${start}${middle}${end}`
}

export function decrypt(cipherText: string, secretKey: string) {
  try {
    // 解码密钥
    const key = Buffer.from(secretKey, 'base64')

    // 分割 IV 和加密内容
    const arr = cipherText.split('~split~')
    if (arr.length < 2) return ''

    // 解构获取IV和数据
    const [ivStr, dataStr] = arr

    // 解码 IV
    const iv = Buffer.from(ivStr!, 'base64')

    // 创建 AES-CBC 解密器
    const decipher = createDecipheriv('aes-128-cbc', key, iv)

    // 解密内容
    let decrypted: string = decipher.update(dataStr!, 'base64', 'utf8')
    decrypted += decipher.final('utf8')

    return decrypted
  } catch (err) {
    console.error(`小红书解密数据失败]:${err instanceof Error ? err.message : '未知错误'}`)
    throw new Error(`小红书解密数据失败]:${err instanceof Error ? err.message : '未知错误'}`)
  }
}

/**
 * 快手 AES 解密
 * 数据格式: {IV}:{加密数据}
 * 算法: AES-128-CBC
 */
export function decryptKuaishouData(encryptedData: string, secretKey: string): string {
  try {
    // 分割IV和加密数据
    const parts = encryptedData.split(':')
    if (parts.length !== 2) {
      throw new Error('快手数据格式错误：应为 {IV}:{加密数据}')
    }

    const [ivBase64, dataBase64] = parts

    if (!ivBase64 || !dataBase64) {
      throw new Error('快手数据格式错误：IV或数据部分为空')
    }

    // Base64解码
    const iv = Buffer.from(ivBase64, 'base64')
    const encryptedBuffer = Buffer.from(dataBase64, 'base64')
    const key = Buffer.from(secretKey, 'base64')

    // 验证密钥长度（AES-128需要16字节）
    if (key.length !== 16) {
      throw new Error(`AES-128密钥长度错误：期望16字节，实际${key.length}字节`)
    }

    // 创建解密器
    const decipher = createDecipheriv('aes-128-cbc', key, iv)

    // 解密
    let decrypted = decipher.update(encryptedBuffer, undefined, 'utf8')
    decrypted += decipher.final('utf8')

    return decrypted
  } catch (error) {
    throw new Error(`快手数据解密失败: ${error instanceof Error ? error.message : '未知错误'}`)
  }
}
