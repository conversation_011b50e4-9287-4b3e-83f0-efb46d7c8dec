import jwt from 'jsonwebtoken'
import { env } from '../env'

const JWT_SECRET = env.JWT_SECRET
const JWT_EXPIRES_IN = '7Day'
const OPEN_API_EXPIRES_IN = '30d' // 开放平台 token 有效期更长

const { sign, verify, decode } = jwt

export interface JWTPayload {
  userId: string
  phone: string
}

export interface OpenAPIJWTPayload {
  userId: string
  appId: string
  type: 'open_api' // 标识这是开放平台的 token
}

/**
 * 生成JWT token
 */
export function generateToken(payload: JWTPayload): string {
  console.log('JWT_SECRET', JWT_SECRET)
  return sign(payload, JWT_SECRET, {
    expiresIn: JWT_EXPIRES_IN,
  })
}

/**
 * 生成开放平台 API token
 */
export function generateOpenAPIToken(payload: OpenAPIJWTPayload): string {
  return sign(payload, JWT_SECRET, {
    expiresIn: OPEN_API_EXPIRES_IN,
  })
}

/**
 * 验证JWT token
 */
export function verifyToken(token: string): JWTPayload {
  try {
    return verify(token, JWT_SECRET) as JWTPayload
  } catch {
    throw new Error('无效的令牌')
  }
}

/**
 * 验证开放平台 API token
 */
export function verifyOpenAPIToken(token: string): OpenAPIJWTPayload {
  try {
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    const payload = verify(token, JWT_SECRET) as any
    if (!payload || payload.type !== 'open_api') {
      throw new Error('不是有效的开放平台令牌')
    }
    return payload as OpenAPIJWTPayload
  } catch {
    throw new Error('无效的开放平台令牌')
  }
}

/**
 * 解码JWT token（不验证签名）
 */
export function decodeToken(token: string): JWTPayload | null {
  try {
    return decode(token) as JWTPayload
  } catch {
    return null
  }
}
