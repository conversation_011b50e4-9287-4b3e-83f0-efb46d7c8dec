import { initTRPC } from '@trpc/server'
import { ZodError } from 'zod'
import type { OpenApiMeta } from 'trpc-to-openapi'
import superjson from 'superjson'
import type { Context } from './context'
import { isdev } from './env'
import { ResponseWrapper, ApiError } from './lib/response'

export const t = initTRPC
  .meta<OpenApiMeta>()
  .context<Context>()
  .create({
    transformer: superjson,
    errorFormatter(opts) {
      const { shape, error, ctx } = opts

      if (ctx?.isPublicApi) {
        if (error.cause instanceof ZodError) {
          const validationErrors = error.cause.flatten().fieldErrors;
          const firstError = Object.values(validationErrors)[0]?.[0] || 'Validation failed';
          return ResponseWrapper.error(40001, firstError) as any;
        }

        if (error instanceof ApiError || error.cause instanceof ApiError) {
            const apiError = (error instanceof ApiError ? error : error.cause) as ApiError;
            return ResponseWrapper.error(apiError.code, apiError.message) as any;
        }

        return ResponseWrapper.error(50000, 'Internal Server Error') as any;
      }

      if (error.code === 'INTERNAL_SERVER_ERROR' && !isdev) {
        return { ...shape, message: 'Internal server error' }
      }
      return {
        ...shape,
        data: {
          ...shape.data,
          zodError: error.code === 'BAD_REQUEST' && error.cause instanceof ZodError ? error.cause.flatten() : null,
        },
      }
    },
  })

export const publicProcedure = t.procedure
export const router = t.router
