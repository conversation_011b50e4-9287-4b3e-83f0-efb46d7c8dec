<div align="center">
  <img src="https://github.com/user-attachments/assets/f9729f43-485a-44d4-b85d-f93dc5c09988" alt="image" height="180"/>
</div>

<h1 align="center"><strong>Fullstack SaaS Boilerplate</strong></h1>
<h3 align="center">Built with <a href="https://fastify.io">Fastify</a>, <a href="https://trpc.io">tRPC</a>, and <a href="https://reactjs.org">React</a>.</h3>

## Project

[![GitHub stars](https://img.shields.io/github/stars/alan345/Fullstack-SaaS-Boilerplate?style=for-the-badge)](https://github.com/alan345/Fullstack-SaaS-Boilerplate/stargazers)
[![GitHub forks](https://img.shields.io/github/forks/alan345/Fullstack-SaaS-Boilerplate?style=for-the-badge)](https://github.com/alan345/Fullstack-SaaS-Boilerplate/network)
[![GitHub license](https://img.shields.io/github/license/alan345/Fullstack-SaaS-Boilerplate?style=for-the-badge)](https://github.com/alan345/Fullstack-SaaS-Boilerplate/blob/master/LICENSE)
[![GitHub issues](https://img.shields.io/github/issues/alan345/Fullstack-SaaS-Boilerplate?style=for-the-badge)](https://github.com/alan345/Fullstack-SaaS-Boilerplate/issues)
[![GitHub issues](https://img.shields.io/badge/Sponsor-me!-blue?style=for-the-badge)](https://github.com/sponsors/alan345)

## Demo

<a href="https://client.ter.work.gd/">
  <img width="200" alt="Demo Fullstack-SaaS-Boilerplate" src="https://github.com/user-attachments/assets/e7341328-dd7b-463a-b822-a491ef81369c">
</a>

<sub>Hosted by [render.com](http://render.com/) for free</sub>

<sub>As it is a free tier, this Demo will spin down with inactivity, which can delay requests by 50 seconds or more. Be patient!</sub>

## Preview

![Fullstack-SaaS-Boilerplate-Demo](https://github.com/user-attachments/assets/4693db07-523a-4326-9bc4-a1b1ea634507)

## Main Stack

| Technology                                        | Description                                               | Stars                                                                                                                                                                          |
| ------------------------------------------------- | --------------------------------------------------------- | ------------------------------------------------------------------------------------------------------------------------------------------------------------------------------ |
| <a href="https://orm.drizzle.team/">Drizzle</a>   | A TypeScript-first ORM for Node.js                        | <a href="https://github.com/drizzle-team/drizzle-orm"><img src="https://img.shields.io/github/stars/drizzle-team/drizzle-orm?style=flat-square" alt="GitHub Repo stars" /></a> |
| <a href="https://fastify.io">Fastify</a>          | Fast, unopinionated, minimalist web framework for Node.js | <a href="https://github.com/fastify/fastify"><img src="https://img.shields.io/github/stars/fastify/fastify?style=flat-square" alt="GitHub Repo stars" /></a>                   |
| <a href="https://www.postgresql.org">Postgres</a> | The world's most advanced open source database            | <a href="https://github.com/postgres/postgres"><img src="https://img.shields.io/github/stars/postgres/postgres?style=flat-square" alt="GitHub Repo stars" /></a>               |
| <a href="https://reactjs.org">React 19</a>        | A JavaScript library for building user interfaces         | <a href="https://github.com/facebook/react"><img src="https://img.shields.io/github/stars/facebook/react?style=flat-square" alt="GitHub Repo stars" /></a>                     |
| <a href="https://tailwindcss.com">Tailwind v4</a> | A utility-first CSS framework for rapid UI development    | <a href="https://github.com/tailwindlabs/tailwindcss"><img src="https://img.shields.io/github/stars/tailwindlabs/tailwindcss?style=flat-square" alt="GitHub Repo stars" /></a> |
| <a href="https://trpc.io">tRPC</a>                | End-to-end typesafe APIs made easy                        | <a href="https://github.com/trpc/trpc"><img src="https://img.shields.io/github/stars/trpc/trpc?style=flat-square" alt="GitHub Repo stars" /></a>                               |

## Other dependencies

| Technology                                                                      | Description                                                      | Stars                                                                                                                                                                        |
| ------------------------------------------------------------------------------- | ---------------------------------------------------------------- | ---------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| <a href="https://better-auth.com">Better Auth</a>                               | Authentication library for Node.js                               | <a href="https://github.com/better-auth/better-auth"><img src="https://img.shields.io/github/stars/better-auth/better-auth?style=flat-square" alt="GitHub Repo stars" /></a> |
| <a href="https://phosphoricons.com">Phosphor Icons</a>                          | Beautifully simple & consistent icons                            | <a href="https://github.com/phosphor-icons/homepage"><img src="https://img.shields.io/github/stars/phosphor-icons/homepage?style=flat-square" alt="GitHub Repo stars" /></a> |
| <a href="https://docs.npmjs.com/cli/v10/using-npm/workspaces">npm Workspace</a> | Workspaces for managing multiple packages in a single repository | <a href="https://github.com/npm/cli"><img src="https://img.shields.io/github/stars/npm/cli?style=flat-square" alt="GitHub Repo stars" /></a>                                 |
| <a href="https://playwright.dev">Playwright</a>                                 | Test your web apps headlessly with a single API                  | <a href="https://github.com/microsoft/playwright"><img src="https://img.shields.io/github/stars/microsoft/playwright?style=flat-square" alt="GitHub Repo stars" /></a>       |
| <a href="https://reactrouter.com">React Router v7</a>                           | Declarative routing for React                                    | <a href="https://github.com/remix-run/react-router"><img src="https://img.shields.io/github/stars/remix-run/react-router?style=flat-square" alt="GitHub Repo stars" /></a>   |
| <a href="https://www.typescriptlang.org">TypeScript</a>                         | TypeScript is a typed superset of JavaScript                     | <a href="https://github.com/microsoft/TypeScript"><img src="https://img.shields.io/github/stars/microsoft/TypeScript?style=flat-square" alt="GitHub Repo stars" /></a>       |
| <a href="https://vitejs.dev">Vite</a>                                           | Next generation frontend tooling. It's fast!                     | <a href="https://github.com/vitejs/vite"><img src="https://img.shields.io/github/stars/vitejs/vite?style=flat-square" alt="GitHub Repo stars" /></a>                         |
| <a href="https://zod.dev">Zod</a>                                               | TypeScript-first schema validation with static type inference    | <a href="https://github.com/colinhacks/zod"><img src="https://img.shields.io/github/stars/colinhacks/zod?style=flat-square" alt="GitHub Repo stars" /></a>                   |
| <a href="https://zustand.docs.pmnd.rs/">Zustand</a>                             | Bear necessities for state management in React                   | <a href="https://github.com/pmndrs/zustand"><img src="https://img.shields.io/github/stars/pmndrs/zustand?style=flat-square" alt="GitHub Repo stars" /></a>                   |

## Features

- [Beers from random-data-api.com](https://random-data-api.com) Example of pulling data from externals REST API
- Health Check for the server (http://localhost:2022/health.trpc) and (http://localhost:2022)
- Search with Debounce Using a Custom Hook
- Chat with Server-Sent Events (SSE). SSE is easier to setup and don't require setting up a WebSocket server.

## Installation

- Update the server `server.env` [file](https://github.com/alan345/Fullstack-SaaS-Boilerplate/blob/main/server.env) and the client `client.env` [file](https://github.com/alan345/Fullstack-SaaS-Boilerplate/blob/main/client/client.env) with your credentials
- Make sure Postgres is running and create a new database called `fsb`

```bash
psql -U user // replace user by your postgres user
CREATE DATABASE fsb;
```

- Run in the terminal In the root directory:

```bash
// Install the dependencies
npm i

// Setup the database
npm run push

// Seed the database
npm run seed

// Run the app (it will run the client and the server automatically)
npm run dev
```

## Building for production

- Update the `.gitignore` [file](https://github.com/alan345/Fullstack-SaaS-Boilerplate/blob/main/.gitignore) by uncommenting `# .env` to ensure your credentials remain private and are not exposed.

```bash
npm run build
npm run start
```

## Printscreens

<img width="1556" alt="image" src="https://github.com/user-attachments/assets/1b3db78d-4b34-4401-b8e3-89dc85e990a9" />
<img width="1556" alt="image" src="https://github.com/user-attachments/assets/5eedf90f-18a8-4687-b8fb-bd4cc5d0dbeb" />
<img width="1556" alt="image" src="https://github.com/user-attachments/assets/eed287fe-7be7-4594-b97a-2ef51718c446" />
<img width="1556" alt="image" src="https://github.com/user-attachments/assets/7a221b78-223b-44e7-8d26-3a1f04c56afd" />
<img width="1556" alt="image" src="https://github.com/user-attachments/assets/fec440d5-ec7a-4cb0-be38-3ee60c193e6a" />
<img width="1556" alt="image" src="https://github.com/user-attachments/assets/d19d0f3f-364b-4a12-a8ca-540c430a09c9" />
<img width="1556" alt="image" src="https://github.com/user-attachments/assets/3c4757ff-9746-459d-bbf0-e3326c4b1ce4" />

## Motivation

Focusing on developer experience: simple, efficient, and fast. This modern stack uses top-tier libraries to build a full-stack web application. Unlike the T3 app (https://create.t3.gg), we opted not to use Next.js, allowing the frontend to remain as static files, easily stored in cloud object storage like AWS S3. Consequently, this stack is designed for building web apps rather than traditional websites, as it is not SEO-friendly.

## End-to-end typesafe with Trpc

![trpc-video-ter](https://github.com/user-attachments/assets/7ee27bbb-5e56-484c-b046-fe0186b4321d)
Video from https://trpc.io

## E2E Testing

The tests should be executed while the application is running.

#### Running the tests in the Terminal

```
npm run test
```

## Other recommendations

- Need a component library? Check out [Chakra UI](https://v2.chakra-ui.com/)
- If your stack is getting more and more shared workspaces, consider using [pnpm](https://pnpm.io/workspaces) instead of npm

## Who is using TER?

- [Nachonacho.com](https://Nachonacho.com) - The world's largest marketplace for Software & Services

Create a PR if you want to add your project here.

## How can you help?

This project is free and open source. If you found it useful, consider giving it a star ⭐ or sponsoring me 💖 — it really helps!
I'm always open to feedback, so feel free to share suggestions to improve the stack.
You’re also welcome to contribute by opening a pull request 🚀.

[![GitHub issues](https://img.shields.io/badge/Sponsor-me!-blue?style=for-the-badge)](https://github.com/sponsors/alan345)

<div align="center">
  <img src="https://github.com/user-attachments/assets/60297acc-81a1-46af-93ec-d281be20757b" alt="image" height="260"/>
</div>
