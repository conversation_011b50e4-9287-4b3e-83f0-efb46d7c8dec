---
alwaysApply: false
---
# 客户端结构

## 路由架构
- **基于文件的路由**：使用 TanStack Router
- **路由文件**：[client/src/routes/](mdc:client/src/routes)
- **认证保护**：`_authenticated/` 目录下的路由需要登录

## 组件组织
- **UI 组件**：[client/src/components/ui/](mdc:client/src/components/ui) - shadcn/ui 组件
- **业务组件**：[client/src/components/](mdc:client/src/components)
- **样式**：Tailwind CSS + CSS Modules

## 状态管理
- **tRPC 客户端**：[client/src/lib/trpc.ts](mdc:client/src/lib/trpc.ts)
- **认证状态**：[client/src/lib/auth/](mdc:client/src/lib/auth)
- **React Query**：自动缓存和同步

## 主要页面
- 登录：[client/src/routes/login.tsx](mdc:client/src/routes/login.tsx)
- 应用管理：[client/src/routes/_authenticated/apps/](mdc:client/src/routes/_authenticated/apps)
- 订单管理：[client/src/routes/_authenticated/orders.tsx](mdc:client/src/routes/_authenticated/orders.tsx)
