---
description: 
globs: 
alwaysApply: true
---
# 服务器端结构

## tRPC 架构
- **tRPC 配置**：[server/src/trpc.ts](mdc:server/src/trpc.ts)
- **路由模块**：[server/src/router/](mdc:server/src/router)
- **中间件**：[server/src/procedure/](mdc:server/src/procedure)
- **上下文**：[server/src/context.ts](mdc:server/src/context.ts)

## 数据层
- **数据库模型**：[server/src/db/](mdc:server/src/db)
- **ORM 配置**：[server/drizzle.config.ts](mdc:server/drizzle.config.ts)
- **业务逻辑**：[server/src/lib/](mdc:server/src/lib)

## API 结构
- **认证路由**：[server/src/router/auth.ts](mdc:server/src/router/auth.ts)
- **应用路由**：[server/src/router/application.ts](mdc:server/src/router/application.ts)
- **订单路由**：[server/src/router/order.ts](mdc:server/src/router/order.ts)
- **余额路由**：[server/src/router/balance.ts](mdc:server/src/router/balance.ts)

## 环境配置
- **环境变量**：[server/src/env.ts](mdc:server/src/env.ts)
- **OpenAPI 文档**：[server/openapi.json](mdc:server/openapi.json)
