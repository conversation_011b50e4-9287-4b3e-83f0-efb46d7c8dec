---
description: 
globs: 
alwaysApply: true
---
# 项目架构

## 整体结构
- **全栈 tRPC 项目**：前后端类型安全通信
- **Monorepo**：使用 pnpm workspace 管理
- **主要目录**：
  - `client/`：React 前端应用
  - `server/`：Fastify + tRPC 后端
  - `tests-e2e/`：端到端测试

## 技术栈
**前端**：React 19 + TanStack Router + Tailwind CSS + shadcn/ui
**后端**：Fastify + tRPC + Drizzle ORM + PostgreSQL + Redis
**认证**：Better Auth
**开发**：TypeScript + ESLint + Playwright

## 关键入口点
- 前端入口：[client/src/main.tsx](mdc:client/src/main.tsx)
- 后端入口：[server/src/index.ts](mdc:server/src/index.ts)
- tRPC 路由：[server/src/router/index.ts](mdc:server/src/router/index.ts)
- 数据库模型：[server/src/db/schema.ts](mdc:server/src/db/schema.ts)
