---
description: 
globs: 
alwaysApply: true
---
# 开发工作流

## 启动命令
- **开发模式**：`pnpm dev` - 同时启动前后端
- **前端**：`pnpm dev:client`
- **后端**：`pnpm dev:server`
- **数据库**：`pnpm studio` - Drizzle Studio

## 构建和部署
- **构建全部**：`pnpm build`
- **类型检查**：`pnpm type-check`
- **测试**：`pnpm test` - E2E 测试

## 数据库操作
- **推送 Schema**：`pnpm push`
- **查看数据**：`pnpm studio`
- **配置文件**：[server/drizzle.config.ts](mdc:server/drizzle.config.ts)

## 代码规范
- **ESLint 配置**：各目录下的 `eslint.config.*`
- **TypeScript 配置**：各目录下的 `tsconfig.json`
- **包管理**：使用 pnpm workspace

## 文件命名约定
- **组件**：kebab-case (如 `loading-button.tsx`)
- **路由**：文件系统路由，特殊前缀 `_` 表示布局
- **API**：REST 风格命名
